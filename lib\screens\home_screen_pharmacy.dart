import 'package:flutter/material.dart';
import 'package:flutter/animation.dart';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'pharmacy_inventory_screen.dart';
import 'pharmacy_reports_screen.dart';
import 'pharmacy_settings_screen.dart';
import 'accepted_orders_screen.dart';

class PharmacyOrdersScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  final VoidCallback onLogout;

  const PharmacyOrdersScreen({
    Key? key,
    required this.isDark,
    required this.onToggleDarkMode,
    required this.onLogout,
  }) : super(key: key);

  @override
  _PharmacyOrdersScreenState createState() => _PharmacyOrdersScreenState();
}

class _PharmacyOrdersScreenState extends State<PharmacyOrdersScreen>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;

  final _scrollController = ScrollController();
  double _scrollOffset = 0;
  bool _isDarkMode = false;
  int _currentIndex = 0;

  final List<Map<String, dynamic>> _orders = [
    {
      'id': 'ORD-2023-001',
      'customer': 'أحمد محمد',
      'medicines': ['باراسيتامول 500mg', 'فيتامين C 1000mg'],
      'time': 'منذ 5 دقائق',
      'status': 'new',
      'prescription': 'assets/images/prescription1.png',
      'offer': null,
      'avatarColor': Colors.blue,
    },
    {
      'id': 'ORD-2023-002',
      'customer': 'سارة خالد',
      'medicines': ['أموكسيسيلين 500mg', 'مضاد حيوي'],
      'time': 'منذ 15 دقيقة',
      'status': 'pending',
      'prescription': null,
      'offer': 75.50,
      'avatarColor': Colors.pink,
    },
    {
      'id': 'ORD-2023-003',
      'customer': 'علي حسن',
      'medicines': ['فيتامين D3', 'كالسيوم', 'ماغنيسيوم'],
      'time': 'منذ 25 دقيقة',
      'status': 'accepted',
      'prescription': 'assets/images/prescription2.png',
      'offer': 120.0,
      'avatarColor': Colors.green,
    },
    {
      'id': 'ORD-2023-004',
      'customer': 'ليلى عبدالله',
      'medicines': ['أدوية ضغط الدم', 'مدر للبول'],
      'time': 'منذ ساعة',
      'status': 'ready',
      'prescription': 'assets/images/prescription3.png',
      'offer': 65.75,
      'avatarColor': Colors.purple,
    },
  ];

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDark;
    _loadDarkMode();

    // تحريك الموجة الخلفية
    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    )..repeat(reverse: true);

    _waveAnimation = Tween<double>(begin: -20, end: 20).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeInOut),
    );



    // متابعة حركة التمرير
    _scrollController.addListener(() {
      setState(() {
        _scrollOffset = _scrollController.offset;
      });
    });
  }

  Future<void> _loadDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('pharmacy_dark_mode') ?? widget.isDark;
    });
  }

  Future<void> _toggleDarkMode() async {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('pharmacy_dark_mode', _isDarkMode);

    // استدعاء الدالة الأصلية إذا كانت موجودة
    widget.onToggleDarkMode();
  }

  @override
  void dispose() {
    _waveController.dispose();

    _scrollController.dispose();
    super.dispose();
  }

  Widget _getCurrentPage() {
    switch (_currentIndex) {
      case 0:
        return _buildOrdersPage();
      case 1:
        return AcceptedOrdersScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
        );
      case 2:
        return PharmacyInventoryScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
        );
      case 3:
        return PharmacyReportsScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
        );
      case 4:
        return PharmacySettingsScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
          onLogout: widget.onLogout,
        );
      default:
        return _buildOrdersPage();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = _isDarkMode ? const Color(0xFF1B1B1B) : Colors.white;
    final textColor = _isDarkMode ? Colors.white : const Color(0xFF1B1B1B);
    const primaryColor = Color(0xFF00BF63);
    final cardColor = _isDarkMode
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFF8F9FA);
    final accentColor = _isDarkMode
        ? const Color(0xFF3A3A3A)
        : const Color(0xFFE8F5E8);

    return Scaffold(
      backgroundColor: bgColor,
      body: _getCurrentPage(),
      bottomNavigationBar: _buildBottomNavBar(primaryColor, textColor),
    );
  }

  Widget _buildOrdersPage() {
    final bgColor = _isDarkMode ? const Color(0xFF1B1B1B) : Colors.white;
    final textColor = _isDarkMode ? Colors.white : const Color(0xFF1B1B1B);
    const primaryColor = Color(0xFF00BF63);
    final cardColor = _isDarkMode
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFF8F9FA);
    final accentColor = _isDarkMode
        ? const Color(0xFF3A3A3A)
        : const Color(0xFFE8F5E8);

    return Stack(
        children: [
          // خلفية متحركة
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _waveAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, _waveAnimation.value),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          primaryColor.withValues(alpha: 0.03),
                          primaryColor.withValues(alpha: 0.01),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // محتوى الشاشة
          CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              SliverAppBar(
                expandedHeight: 280,
                floating: false,
                pinned: true,
                backgroundColor: bgColor,
                elevation: 0,
                leading: Container(),
                flexibleSpace: FlexibleSpaceBar(
                  title: Transform.translate(
                    offset: Offset(0, _scrollOffset > 150 ? 0 : 20),
                    child: Opacity(
                      opacity: _scrollOffset > 150 ? 1.0 : 0.0,
                      child: Text(
                        'لوحة تحكم الصيدلية',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                    ),
                  ),
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          primaryColor.withValues(alpha: 0.1),
                          primaryColor.withValues(alpha: 0.05),
                          Colors.transparent,
                        ],
                      ),
                    ),
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            // Header with profile and dark mode
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Profile section
                                Row(
                                  children: [
                                    Container(
                                      width: 50,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: LinearGradient(
                                          colors: [
                                            primaryColor,
                                            primaryColor.withValues(alpha: 0.7),
                                          ],
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: primaryColor.withValues(alpha: 0.3),
                                            blurRadius: 10,
                                            offset: const Offset(0, 4),
                                          ),
                                        ],
                                      ),
                                      child: const Icon(
                                        Icons.local_pharmacy,
                                        color: Colors.white,
                                        size: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'مرحباً بك',
                                          style: TextStyle(
                                            color: textColor.withValues(alpha: 0.7),
                                            fontSize: 14,
                                            fontFamily: 'Tajawal',
                                          ),
                                        ),
                                        Text(
                                          'صيدلية النور',
                                          style: TextStyle(
                                            color: textColor,
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            fontFamily: 'Tajawal',
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                // Dark mode toggle
                                Container(
                                  decoration: BoxDecoration(
                                    color: accentColor,
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.1),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: IconButton(
                                    icon: AnimatedSwitcher(
                                      duration: const Duration(milliseconds: 300),
                                      child: Icon(
                                        _isDarkMode ? Icons.light_mode : Icons.dark_mode,
                                        key: ValueKey(_isDarkMode),
                                        color: primaryColor,
                                        size: 24,
                                      ),
                                    ),
                                    onPressed: () async {
                                      await _toggleDarkMode();
                                      // إظهار رسالة تأكيد
                                      if (mounted) {
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              _isDarkMode
                                                  ? 'تم التبديل إلى الوضع المظلم'
                                                  : 'تم التبديل إلى الوضع الفاتح',
                                              style: const TextStyle(
                                                fontFamily: 'Tajawal',
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            backgroundColor: primaryColor,
                                            duration: const Duration(milliseconds: 1500),
                                            behavior: SnackBarBehavior.floating,
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(10),
                                            ),
                                          ),
                                        );
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 30),
                            // Welcome message with animation
                            AnimatedBuilder(
                              animation: _waveController,
                              builder: (context, child) {
                                return Transform.translate(
                                  offset: Offset(0, _waveController.value * 2),
                                  child: Column(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(16),
                                        decoration: BoxDecoration(
                                          color: primaryColor.withValues(alpha: 0.1),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.medical_services,
                                          size: 40,
                                          color: primaryColor,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'إدارة طلبات الأدوية',
                                        style: TextStyle(
                                          color: textColor,
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                          fontFamily: 'Tajawal',
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'تابع وأدر جميع طلبات العملاء بسهولة',
                                        style: TextStyle(
                                          color: textColor.withValues(alpha: 0.6),
                                          fontSize: 16,
                                          fontFamily: 'Tajawal',
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إحصائيات اليوم',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          _buildStatCard(
                            'طلبات جديدة',
                            '12',
                            Icons.new_releases,
                            const Color(0xFF2196F3),
                            textColor,
                            cardColor,
                            accentColor,
                          ),
                          const SizedBox(width: 12),
                          _buildStatCard(
                            'قيد التجهيز',
                            '8',
                            Icons.timer,
                            const Color(0xFFFF9800),
                            textColor,
                            cardColor,
                            accentColor,
                          ),
                          const SizedBox(width: 12),
                          _buildStatCard(
                            'جاهزة للتسليم',
                            '5',
                            Icons.delivery_dining,
                            primaryColor,
                            textColor,
                            cardColor,
                            accentColor,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'الطلبات الحديثة',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '${_orders.length} طلب',
                          style: TextStyle(
                            color: primaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Tajawal',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
                  final order = _orders[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                    child: _buildOrderCard(
                      order,
                      index,
                      textColor,
                      cardColor,
                      primaryColor,
                    ),
                  );
                }, childCount: _orders.length),
              ),
            ],
          ),


        ],
      );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    Color textColor,
    Color bgColor,
    Color accentColor,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.bold,
                fontSize: 24,
                fontFamily: 'Tajawal',
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                color: textColor.withValues(alpha: 0.7),
                fontSize: 12,
                fontFamily: 'Tajawal',
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderCard(
    Map<String, dynamic> order,
    int index,
    Color textColor,
    Color cardColor,
    Color primaryColor,
  ) {
    final statusInfo = _getStatusInfo(order['status']);
    final avatarColor = order['avatarColor'] ?? Colors.grey;

    return AnimatedBuilder(
      animation: _waveController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            0,
            sin(_waveController.value * 2 * pi + index * 0.5) * 2,
          ),
          child: child,
        );
      },
      child: Container(
        margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        child: Material(
          borderRadius: BorderRadius.circular(16),
          color: cardColor,
          elevation: 4,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () => _showOrderDetails(order),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: avatarColor.withValues(alpha: 0.2),
                        child: Text(
                          order['customer'].toString().substring(0, 1),
                          style: TextStyle(
                            color: avatarColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              order['customer'],
                              style: TextStyle(
                                color: textColor,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Tajawal',
                              ),
                            ),
                            Text(
                              order['time'],
                              style: TextStyle(
                                color: textColor.withValues(alpha: 0.6),
                                fontSize: 12,
                                fontFamily: 'Tajawal',
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: statusInfo.color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              statusInfo.icon,
                              size: 16,
                              color: statusInfo.color,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              statusInfo.text,
                              style: TextStyle(
                                color: statusInfo.color,
                                fontFamily: 'Tajawal',
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    order['medicines'].join('، '),
                    style: TextStyle(
                      color: textColor.withValues(alpha: 0.8),
                      fontFamily: 'Tajawal',
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (order['prescription'] != null) ...[
                    const SizedBox(height: 12),
                    GestureDetector(
                      onTap: () =>
                          _showPrescriptionImage(order['prescription']),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.asset(
                          order['prescription'],
                          height: 80,
                          width: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ],
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      if (order['status'] == 'new')
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: primaryColor,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            onPressed: () => _showOfferDialog(order),
                            child: const Text(
                              'تقديم عرض',
                              style: TextStyle(fontFamily: 'Tajawal'),
                            ),
                          ),
                        ),
                      if (order['offer'] != null)
                        Expanded(
                          child: Text(
                            'العرض: ${order['offer']} ر.س',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: primaryColor,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Tajawal',
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      if (order['status'] == 'pending')
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            onPressed: () => _acceptOrder(order),
                            child: const Text(
                              'قبول الطلب',
                              style: TextStyle(fontFamily: 'Tajawal'),
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showOrderDetails(Map<String, dynamic> order) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: _isDarkMode ? const Color(0xFF1E293B) : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'تفاصيل الطلب',
                style: TextStyle(
                  color: _isDarkMode ? Colors.white : const Color(0xFF0F172A),
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Tajawal',
                ),
              ),
              const SizedBox(height: 24),
              _buildDetailRow('رقم الطلب', order['id']),
              _buildDetailRow('العميل', order['customer']),
              _buildDetailRow('الوقت', order['time']),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              Text(
                'الأدوية المطلوبة',
                style: TextStyle(
                  color: _isDarkMode ? Colors.white : const Color(0xFF0F172A),
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Tajawal',
                ),
              ),
              const SizedBox(height: 12),
              ...order['medicines']
                  .map<Widget>(
                    (medicine) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          Icon(
                            Icons.medical_services,
                            size: 16,
                            color: const Color(0xFF00C853),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              medicine,
                              style: TextStyle(
                                color: _isDarkMode
                                    ? Colors.white
                                    : const Color(0xFF0F172A),
                                fontFamily: 'Tajawal',
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                  .toList(),
              const SizedBox(height: 24),
              if (order['prescription'] != null)
                Column(
                  children: [
                    const Text(
                      'صورة الوصفة الطبية',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                    const SizedBox(height: 12),
                    GestureDetector(
                      onTap: () =>
                          _showPrescriptionImage(order['prescription']),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.asset(
                          order['prescription'],
                          height: 150,
                          width: double.infinity,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ],
                ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF00C853),
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'إغلاق',
                  style: TextStyle(fontFamily: 'Tajawal'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: TextStyle(
              color: _isDarkMode ? Colors.white70 : Colors.grey[700],
              fontFamily: 'Tajawal',
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: _isDarkMode ? Colors.white : Colors.black,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal',
            ),
          ),
        ],
      ),
    );
  }

  void _showPrescriptionImage(String imagePath) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(16),
          child: InteractiveViewer(
            panEnabled: true,
            minScale: 0.5,
            maxScale: 3.0,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Image.asset(imagePath, fit: BoxFit.contain),
            ),
          ),
        );
      },
    );
  }

  void _showOfferDialog(Map<String, dynamic> order) {
    final priceController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'تقديم عرض سعر',
                  style: TextStyle(
                    color: _isDarkMode ? Colors.white : Colors.black,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Tajawal',
                  ),
                ),
                const SizedBox(height: 24),
                TextField(
                  controller: priceController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'السعر (ر.س)',
                    prefixIcon: const Icon(Icons.attach_money),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: _isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.grey[100],
                  ),
                  style: TextStyle(
                    color: _isDarkMode ? Colors.white : Colors.black,
                    fontFamily: 'Tajawal',
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'إلغاء',
                          style: TextStyle(fontFamily: 'Tajawal'),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          if (priceController.text.isNotEmpty) {
                            Navigator.pop(context);
                            _submitOffer(
                              order,
                              double.parse(priceController.text),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF00C853),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'إرسال العرض',
                          style: TextStyle(fontFamily: 'Tajawal'),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _submitOffer(Map<String, dynamic> order, double price) {
    setState(() {
      order['offer'] = price;
      order['status'] = 'pending';
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(
          'تم إرسال العرض بنجاح',
          style: TextStyle(fontFamily: 'Tajawal'),
        ),
        backgroundColor: const Color(0xFF00C853),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _acceptOrder(Map<String, dynamic> order) {
    setState(() {
      order['status'] = 'accepted';
    });

    // انتقال تلقائي إلى صفحة الطلبات المقبولة
    setState(() {
      _currentIndex = 1; // الطلبات المقبولة
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'تم قبول الطلب بنجاح',
          style: TextStyle(fontFamily: 'Tajawal'),
        ),
        backgroundColor: Color(0xFF00C853),
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 2),
      ),
    );
  }



  _StatusInfo _getStatusInfo(String status) {
    switch (status) {
      case 'new':
        return _StatusInfo(Colors.blue, Icons.new_releases, 'جديد');
      case 'pending':
        return _StatusInfo(Colors.orange, Icons.timer, 'بانتظار القبول');
      case 'accepted':
        return _StatusInfo(
          const Color(0xFF00C853),
          Icons.check_circle,
          'مقبول',
        );
      case 'ready':
        return _StatusInfo(
          Colors.purple,
          Icons.delivery_dining,
          'جاهز للتسليم',
        );
      default:
        return _StatusInfo(Colors.grey, Icons.info, 'غير معروف');
    }
  }

  Widget _buildBottomNavBar(Color primaryColor, Color textColor) {
    return BottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
      backgroundColor: _isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
      selectedItemColor: primaryColor,
      unselectedItemColor: textColor.withValues(alpha: 0.5),
      iconSize: 20,
      selectedFontSize: 10,
      unselectedFontSize: 9,
      selectedLabelStyle: const TextStyle(
        fontFamily: 'Tajawal',
        fontSize: 10,
        fontWeight: FontWeight.bold,
      ),
      unselectedLabelStyle: const TextStyle(fontFamily: 'Tajawal', fontSize: 9),
      type: BottomNavigationBarType.fixed,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.assignment), label: 'الطلبات'),
        BottomNavigationBarItem(icon: Icon(Icons.assignment_turned_in), label: 'المقبولة'),
        BottomNavigationBarItem(icon: Icon(Icons.inventory), label: 'المخزون'),
        BottomNavigationBarItem(icon: Icon(Icons.analytics), label: 'التقارير'),
        BottomNavigationBarItem(icon: Icon(Icons.settings), label: 'الإعدادات'),
      ],
    );
  }
}

class _StatusInfo {
  final Color color;
  final IconData icon;
  final String text;

  _StatusInfo(this.color, this.icon, this.text);
}
