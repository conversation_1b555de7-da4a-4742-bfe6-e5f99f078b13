import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';
import 'app/app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  runApp(
    EasyLocalization(
      supportedLocales: const [Locale('en'), Locale('ar')],
      path: 'lib/translations',
      fallbackLocale: const Locale('ar'),
      startLocale: const Locale('ar'),
      child: const MedicineDeliveryApp(),
    ),
  );
}

class MyAppWithDynamicSystemUi extends StatelessWidget {
  const MyAppWithDynamicSystemUi({super.key});

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        final Brightness brightness =
            WidgetsBinding.instance.window.platformBrightness;
        final bool isDark = brightness == Brightness.dark;
        final Color navBarColor = isDark
            ? const Color(0xFF1B1B1B)
            : Colors.white;
        final Brightness navBarIconBrightness = isDark
            ? Brightness.light
            : Brightness.dark;
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: isDark
                ? Brightness.light
                : Brightness.dark,
            systemNavigationBarColor: navBarColor,
            systemNavigationBarIconBrightness: navBarIconBrightness,
          ),
          child: const MedicineDeliveryApp(),
        );
      },
    );
  }
}
