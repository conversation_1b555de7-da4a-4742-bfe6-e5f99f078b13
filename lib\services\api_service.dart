import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

class ApiService {
  // استخدام الإعدادات من AppConfig
  static String get baseUrl => AppConfig.baseUrl;
  
  // Singleton pattern
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  String? _accessToken;
  String? _refreshToken;

  // Initialize tokens from storage
  Future<void> initializeTokens() async {
    final prefs = await SharedPreferences.getInstance();
    _accessToken = prefs.getString('access_token');
    _refreshToken = prefs.getString('refresh_token');
  }

  // Save tokens to storage
  Future<void> saveTokens(String accessToken, String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    _accessToken = accessToken;
    _refreshToken = refreshToken;
    await prefs.setString('access_token', accessToken);
    await prefs.setString('refresh_token', refreshToken);
  }

  // Clear tokens from storage
  Future<void> clearTokens() async {
    final prefs = await SharedPreferences.getInstance();
    _accessToken = null;
    _refreshToken = null;
    await prefs.remove('access_token');
    await prefs.remove('refresh_token');
  }

  // Get headers with authorization
  Map<String, String> get headers {
    final Map<String, String> headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    if (_accessToken != null) {
      headers['Authorization'] = 'Bearer $_accessToken';
    }
    
    return headers;
  }

  // Handle API response
  Map<String, dynamic> _handleResponse(http.Response response) {
    try {
      final Map<String, dynamic> data = json.decode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return data;
      } else {
        throw ApiException(
          message: data['message'] ?? 'حدث خطأ غير متوقع',
          statusCode: response.statusCode,
          errors: data['errors'],
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }

      // Handle JSON parsing errors or network errors
      throw ApiException(
        message: 'خطأ في الاتصال بالخادم',
        statusCode: response.statusCode,
      );
    }
  }

  // Refresh access token
  Future<bool> refreshAccessToken() async {
    if (_refreshToken == null) return false;

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/refresh-token'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'refreshToken': _refreshToken}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        await saveTokens(
          data['data']['accessToken'],
          data['data']['refreshToken'],
        );
        return true;
      }
    } catch (e) {
      print('Token refresh failed: $e');
    }

    return false;
  }

  // Make authenticated request with token refresh
  Future<Map<String, dynamic>> _makeAuthenticatedRequest(
    Future<http.Response> Function() request,
  ) async {
    try {
      final response = await request();
      return _handleResponse(response);
    } catch (e) {
      if (e is ApiException && e.statusCode == 401) {
        // Try to refresh token
        final refreshed = await refreshAccessToken();
        if (refreshed) {
          // Retry the request with new token
          final response = await request();
          return _handleResponse(response);
        } else {
          // Refresh failed, clear tokens and throw
          await clearTokens();
          throw ApiException(
            message: 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى',
            statusCode: 401,
          );
        }
      }
      rethrow;
    }
  }

  // Auth endpoints
  Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String phone,
    required String password,
    required String address,
    required String governorate,
  }) async {
    try {
      // طباعة معلومات التشخيص
      AppConfig.printNetworkInfo();
      print('Attempting to register with URL: $baseUrl/auth/register');

      final response = await http.post(
        Uri.parse('$baseUrl/auth/register'),
        headers: headers,
        body: json.encode({
          'name': name,
          'email': email,
          'phone': phone,
          'password': password,
          'address': address,
          'governorate': governorate,
        }),
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw ApiException(
            message: AppConfig.timeoutErrorMessage,
            statusCode: 408,
          );
        },
      );

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      final data = _handleResponse(response);

      // Save tokens if registration successful
      if (data['success'] && data['data']['accessToken'] != null) {
        await saveTokens(
          data['data']['accessToken'],
          data['data']['refreshToken'],
        );
      }

      return data;
    } catch (e) {
      print('Registration error: $e');

      if (e is ApiException) {
        rethrow;
      }

      // Handle specific network errors
      String errorMessage = 'خطأ في الاتصال';

      if (e.toString().contains('SocketException') ||
          e.toString().contains('NetworkException') ||
          e.toString().contains('Connection refused')) {
        errorMessage = 'لا يمكن الاتصال بالخادم. تأكد من:\n'
                     '• اتصالك بالإنترنت\n'
                     '• أن الهاتف والكمبيوتر على نفس الشبكة\n'
                     '• أن الخادم يعمل على العنوان: $baseUrl';
      } else if (e.toString().contains('TimeoutException')) {
        errorMessage = 'انتهت مهلة الاتصال. تأكد من سرعة الإنترنت وحاول مرة أخرى';
      } else if (e.toString().contains('FormatException')) {
        errorMessage = 'خطأ في تنسيق البيانات المستلمة من الخادم';
      }

      throw ApiException(
        message: errorMessage,
        statusCode: 0,
      );
    }
  }

  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/login'),
      headers: headers,
      body: json.encode({
        'email': email,
        'password': password,
      }),
    );

    final data = _handleResponse(response);
    
    // Save tokens if login successful
    if (data['success'] && data['data']['accessToken'] != null) {
      await saveTokens(
        data['data']['accessToken'],
        data['data']['refreshToken'],
      );
    }
    
    return data;
  }

  Future<Map<String, dynamic>> loginUser(String email, String password) async {
    final url = Uri.parse('https://your-api-url.com/login');
    final response = await http.post(
      url,
      body: {
        'email': email,
        'password': password,
      },
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      return {
        'success': false,
        'message': 'فشل الاتصال بالخادم',
      };
    }
  }

  Future<Map<String, dynamic>> verifyEmailWithCode(String email, String code) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/verify-email'),
      headers: headers,
      body: json.encode({
        'email': email,
        'code': code,
      }),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> resendVerificationCode(String email) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/resend-verification-code'),
      headers: headers,
      body: json.encode({'email': email}),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> forgotPassword(String email) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/forgot-password'),
      headers: headers,
      body: json.encode({'email': email}),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> resetPassword({
    required String token,
    required String password,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/reset-password'),
      headers: headers,
      body: json.encode({
        'token': token,
        'password': password,
      }),
    );

    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    return await _makeAuthenticatedRequest(() => http.post(
      Uri.parse('$baseUrl/auth/change-password'),
      headers: headers,
      body: json.encode({
        'currentPassword': currentPassword,
        'newPassword': newPassword,
        'confirmPassword': confirmPassword,
      }),
    ));
  }

  Future<Map<String, dynamic>> logout() async {
    final data = await _makeAuthenticatedRequest(() => http.post(
      Uri.parse('$baseUrl/auth/logout'),
      headers: headers,
      body: json.encode({'refreshToken': _refreshToken}),
    ));
    
    await clearTokens();
    return data;
  }

  Future<Map<String, dynamic>> getCurrentUser() async {
    return await _makeAuthenticatedRequest(() => http.get(
      Uri.parse('$baseUrl/auth/me'),
      headers: headers,
    ));
  }

  // User profile endpoints
  Future<Map<String, dynamic>> getUserProfile() async {
    return await _makeAuthenticatedRequest(() => http.get(
      Uri.parse('$baseUrl/users/profile'),
      headers: headers,
    ));
  }

  Future<Map<String, dynamic>> updateUserProfile({
    String? name,
    String? phone,
    String? address,
    String? governorate,
  }) async {
    final Map<String, dynamic> body = {};
    if (name != null) body['name'] = name;
    if (phone != null) body['phone'] = phone;
    if (address != null) body['address'] = address;
    if (governorate != null) body['governorate'] = governorate;

    return await _makeAuthenticatedRequest(() => http.put(
      Uri.parse('$baseUrl/users/profile'),
      headers: headers,
      body: json.encode(body),
    ));
  }

  // Check if user is logged in
  bool get isLoggedIn => _accessToken != null;

  // Get current access token
  String? get accessToken => _accessToken;
}

// Custom exception class for API errors
class ApiException implements Exception {
  final String message;
  final int statusCode;
  final List<String>? errors;

  ApiException({
    required this.message,
    required this.statusCode,
    this.errors,
  });

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode)';
  }
}
