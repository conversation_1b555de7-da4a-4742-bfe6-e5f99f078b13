import 'package:flutter/material.dart';
import 'package:pharmacy/widgets/three_d_card.dart';
import 'order_tracking_screen.dart';
import 'home_screen.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:ui';
import 'request_medicine_screen.dart';

class OffersScreen extends StatefulWidget {
  final String requestId;
  final bool isDark;
  final VoidCallback onToggleDarkMode;

  const OffersScreen({
    super.key,
    required this.requestId,
    this.isDark = true,
    required this.onToggleDarkMode,
  });

  @override
  _OffersScreenState createState() => _OffersScreenState();
}

class _OffersScreenState extends State<OffersScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  List<Map<String, dynamic>> _offers = [];
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _animationController.forward();
    _loadOffers();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadOffers() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      setState(() {
        _offers = [
          {
            'id': '1',
            'pharmacy': 'صيدلية النور',
            'pharmacy_image': 'assets/images/pharmacy.png',
            'price': 45.0,
            'original_price': 50.0,
            'deliveryTime': '20 دقيقة',
            'details': 'خصم 10% على الطلب',
            'distance': '1.2 كم',
            'rating': 4.8,
            'commission': '5%',
          },
          {
            'id': '2',
            'pharmacy': 'صيدلية الشفاء',
            'pharmacy_image': 'assets/images/pharmacy.png',
            'price': 48.0,
            'original_price': 55.0,
            'deliveryTime': '15 دقيقة',
            'details': 'توصيل سريع',
            'distance': '0.8 كم',
            'rating': 4.9,
            'commission': '7%',
          },
          {
            'id': '3',
            'pharmacy': 'صيدلية الأمل',
            'pharmacy_image': 'assets/images/pharmacy.png',
            'price': 42.0,
            'original_price': 45.0,
            'deliveryTime': '25 دقيقة',
            'details': 'أفضل سعر',
            'distance': '1.5 كم',
            'rating': 4.7,
            'commission': '3%',
          },
        ];
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في تحميل العروض: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = widget.isDark ? const Color(0xFF121212) : Colors.white;
    final textColor = widget.isDark ? Colors.white : const Color(0xFF121212);
    final cardColor = widget.isDark ? const Color(0xFF1E1E1E) : Colors.white;
    final secondaryTextColor = widget.isDark
        ? Colors.grey[400]
        : Colors.grey[600];

    final bool noRequest = widget.requestId.isEmpty;

    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        iconTheme: IconThemeData(color: const Color(0xFF00BF63)),
        title: const Text(
          'العروض',
          style: TextStyle(
            color: Color(0xFF00BF63),
            fontWeight: FontWeight.bold,
            fontFamily: 'Tajawal',
            fontSize: 20,
          ),
        ),
      ),
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 500),
        child: noRequest
            ? _buildNoRequestMessage(context, textColor)
            : _isLoading
            ? _buildLoadingState(textColor)
            : _hasError
            ? _buildErrorState(textColor)
            : _buildOffersList(textColor, cardColor, secondaryTextColor),
      ),
    );
  }

  Widget _buildLoadingState(Color textColor) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: Color(0xFF00BF63),
            strokeWidth: 2,
          ),
          const SizedBox(height: 20),
          Text(
            'جاري البحث عن أفضل العروض...',
            style: TextStyle(
              color: textColor,
              fontSize: 16,
              fontFamily: 'Tajawal',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قد يستغرق هذا بضع ثوانٍ',
            style: TextStyle(
              color: textColor.withValues(alpha: 0.7),
              fontSize: 14,
              fontFamily: 'Tajawal',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Color textColor) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 20),
          Text(
            'حدث خطأ في تحميل العروض',
            style: TextStyle(
              color: textColor,
              fontSize: 18,
              fontFamily: 'Tajawal',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadOffers,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF00BF63),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text(
              'إعادة المحاولة',
              style: TextStyle(
                fontFamily: 'Tajawal',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOffersList(
    Color textColor,
    Color cardColor,
    Color? secondaryTextColor,
  ) {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Icon(
                  Icons.local_offer_outlined,
                  color: const Color(0xFF00BF63),
                ),
                const SizedBox(width: 8),
                Text(
                  '${_offers.length} عرض متاح',
                  style: TextStyle(
                    color: textColor,
                    fontFamily: 'Tajawal',
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              itemCount: _offers.length,
              itemBuilder: (context, index) {
                final offer = _offers[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: OfferCard(
                    offer: offer,
                    onAccept: () => _acceptOffer(offer['id']),
                    isDark: widget.isDark,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoRequestMessage(BuildContext context, Color textColor) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(Icons.local_offer, color: const Color(0xFF00BF63), size: 60),
            const SizedBox(height: 24),
            Text(
              'لا توجد عروض متاحة حالياً',
              style: TextStyle(
                color: textColor,
                fontSize: 20,
                fontWeight: FontWeight.bold,
                fontFamily: 'Tajawal',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'قم بطلب دواء أولاً لعرض العروض المتاحة من الصيدليات القريبة.',
              style: TextStyle(
                color: textColor.withValues(alpha: 0.7),
                fontSize: 15,
                fontFamily: 'Tajawal',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 28),
            ElevatedButton.icon(
              icon: const Icon(Icons.add_circle_outline),
              label: const Text('طلب دواء'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00BF63),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 14,
                ),
                textStyle: const TextStyle(
                  fontFamily: 'Tajawal',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              onPressed: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (_) => RequestMedicineScreen(
                      isDark: widget.isDark,
                      onToggleDarkMode: widget.onToggleDarkMode,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _acceptOffer(String offerId) async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          backgroundColor: widget.isDark
              ? const Color(0xFF1E1E1E)
              : Colors.white,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(color: Color(0xFF00BF63)),
              const SizedBox(height: 20),
              Text(
                'جاري تأكيد الطلب...',
                style: TextStyle(
                  color: widget.isDark ? Colors.white : const Color(0xFF121212),
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
        ),
      );

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      Navigator.pop(context); // Close loading dialog

      // Navigate to order tracking
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => OrderTrackingScreen(
            isDark: widget.isDark,
            onToggleDarkMode: widget.onToggleDarkMode,
            pharmacyAddress:
                'عنوان الصيدلية: ${_offers.firstWhere((o) => o['id'] == offerId)['pharmacy']}',
            userAddress: 'موقعك الحالي',
          ),
        ),
      );
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}

class OfferCard extends StatefulWidget {
  final Map<String, dynamic> offer;
  final VoidCallback onAccept;
  final bool isDark;

  const OfferCard({
    super.key,
    required this.offer,
    required this.onAccept,
    required this.isDark,
  });

  @override
  State<OfferCard> createState() => _OfferCardState();
}

class _OfferCardState extends State<OfferCard> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardColor = widget.isDark ? const Color(0xFF1E1E1E) : Colors.white;
    final textColor = widget.isDark ? Colors.white : const Color(0xFF121212);
    final secondaryTextColor = widget.isDark
        ? Colors.grey[400]
        : Colors.grey[600];
    final discountColor = const Color(0xFFE53935);

    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: widget.onAccept,
      child: AnimatedScale(
        duration: const Duration(milliseconds: 100),
        scale: _isPressed ? 0.98 : 1.0,
        child: Container(
          decoration: BoxDecoration(
            color: cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Pharmacy Info Row
              Row(
                children: [
                  // Pharmacy Image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child:
                          widget.offer['pharmacy_image'].toString().startsWith(
                            'http',
                          )
                          ? Image.network(
                              widget.offer['pharmacy_image'],
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Image.asset(
                                  'assets/images/pharmacy.png',
                                  fit: BoxFit.cover,
                                );
                              },
                            )
                          : Image.asset(
                              widget.offer['pharmacy_image'],
                              fit: BoxFit.cover,
                            ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Pharmacy Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.offer['pharmacy'],
                          style: TextStyle(
                            color: textColor,
                            fontFamily: 'Tajawal',
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.star, color: Colors.amber, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              widget.offer['rating'].toString(),
                              style: TextStyle(
                                color: secondaryTextColor,
                                fontFamily: 'Tajawal',
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Icon(
                              Icons.location_on_outlined,
                              color: const Color(0xFF00BF63),
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              widget.offer['distance'],
                              style: TextStyle(
                                color: secondaryTextColor,
                                fontFamily: 'Tajawal',
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.offer['details'],
                          style: TextStyle(
                            color: const Color(0xFF00BF63),
                            fontFamily: 'Tajawal',
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Price and Delivery Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Price Column
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'السعر',
                        style: TextStyle(
                          color: secondaryTextColor,
                          fontFamily: 'Tajawal',
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '${widget.offer['price']} ج.م',
                            style: TextStyle(
                              color: textColor,
                              fontFamily: 'Tajawal',
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (widget.offer['original_price'] != null)
                            Text(
                              '${widget.offer['original_price']} ج.م',
                              style: TextStyle(
                                color: discountColor,
                                fontFamily: 'Tajawal',
                                decoration: TextDecoration.lineThrough,
                                fontSize: 14,
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),

                  // Delivery Column
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'وقت التوصيل',
                        style: TextStyle(
                          color: secondaryTextColor,
                          fontFamily: 'Tajawal',
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.offer['deliveryTime'],
                        style: TextStyle(
                          color: textColor,
                          fontFamily: 'Tajawal',
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Commission Info
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF00BF63).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: const Color(0xFF00BF63),
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'يشمل عمولة ${widget.offer['commission']}',
                      style: TextStyle(
                        color: const Color(0xFF00BF63),
                        fontFamily: 'Tajawal',
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Accept Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: widget.onAccept,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF00BF63),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 14),
                  ),
                  child: const Text(
                    'قبول العرض',
                    style: TextStyle(
                      fontFamily: 'Tajawal',
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
