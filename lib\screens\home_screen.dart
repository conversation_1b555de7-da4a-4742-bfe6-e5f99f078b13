import 'package:flutter/material.dart';
import 'offers_screen.dart';
import 'request_medicine_screen.dart';
import 'chatbot_screen.dart';
import 'profile_tab.dart';
import 'medicines_screen.dart';
import 'order_tracking_screen.dart';
import 'dart:ui';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:io'; // Added for File
import 'package:shared_preferences/shared_preferences.dart'; // Added for SharedPreferences
import '../services/user_service.dart';

const Color kInDriveGreen = Color(0xFF00BF63);
const Color kDarkGrey = Color(0xFF1B1B1B);
const Color kInactiveGrey = Color(0xFF888888);

class HomeScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  final VoidCallback? onLogout;
  const HomeScreen({
    super.key,
    this.isDark = true,
    required this.onToggleDarkMode,
    this.onLogout,
  });

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  int _selectedIndex = 0;
  final ScrollController _scrollController = ScrollController();
  bool _showAppBar = true;
  double _lastOffset = 0;
  bool _isDarkMode = false;

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDark;
    _loadDarkMode();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );
    _animationController.forward();
    _scrollController.addListener(_handleScroll);
  }

  Future<void> _loadDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('user_dark_mode') ?? widget.isDark;
    });
  }

  Future<void> _toggleDarkMode() async {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('user_dark_mode', _isDarkMode);

    // استدعاء الدالة الأصلية إذا كانت موجودة
    widget.onToggleDarkMode();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _handleScroll() {
    final offset = _scrollController.offset;
    if (offset > _lastOffset && offset > 40 && _showAppBar) {
      setState(() => _showAppBar = false);
    } else if (offset < _lastOffset && !_showAppBar) {
      setState(() => _showAppBar = true);
    }
    _lastOffset = offset;
  }

  void _onTabSelected(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  void _openOrderMedicine() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RequestMedicineScreen(
          isDark: widget.isDark,
          onToggleDarkMode: widget.onToggleDarkMode,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = _isDarkMode ? kDarkGrey : Colors.white;
    final textColor = _isDarkMode ? Colors.white : kDarkGrey;
    final List<Widget> _pages = [
      MainHomeTab(
        isDark: _isDarkMode,
        onToggleDarkMode: _toggleDarkMode,
        onQuickActionTap: (i) {
          if (i == 2) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RequestMedicineScreen(
                  isDark: _isDarkMode,
                  onToggleDarkMode: _toggleDarkMode,
                ),
              ),
            );
          } else {
            setState(() => _selectedIndex = i);
          }
        },
        onProfileTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => ProfileTab(
                isDark: _isDarkMode,
                onToggleDarkMode: _toggleDarkMode,
                onLogout: widget.onLogout,
              ),
            ),
          );
        },
        scrollController: _scrollController,
      ),
      MedicinesScreen(
        isDark: _isDarkMode,
        onToggleDarkMode: (val) => _toggleDarkMode(),
        onOrderPressed: () {
          setState(() {
            _selectedIndex = 3;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة الدواء إلى سلة الطلبات'),
              backgroundColor: kInDriveGreen,
            ),
          );
        },
      ),
      RequestMedicineScreen(
        isDark: _isDarkMode,
        onToggleDarkMode: _toggleDarkMode,
      ),
      OffersScreen(
        requestId: '1',
        isDark: _isDarkMode,
        onToggleDarkMode: _toggleDarkMode,
      ),
      ChatBotScreen(
        isDark: _isDarkMode,
        onToggleDarkMode: _toggleDarkMode,
      ),
      OrderTrackingScreen(
        isDark: _isDarkMode,
        onToggleDarkMode: _toggleDarkMode,
      ),
    ];
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: bgColor,
        appBar: _selectedIndex == 0
            ? AppBar(
                backgroundColor: bgColor,
                elevation: 0,
                iconTheme: IconThemeData(color: kInDriveGreen),
                title: Text(
                  'الرئيسية',
                  style: TextStyle(
                    color: textColor,
                    fontFamily: 'Poppins',
                    fontWeight: FontWeight.bold,
                  ),
                ),
                actions: [
                  Container(
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: _isDarkMode ? const Color(0xFF2A2A2A) : const Color(0xFFE8F5E8),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        child: Icon(
                          _isDarkMode ? Icons.light_mode : Icons.dark_mode,
                          key: ValueKey(_isDarkMode),
                          color: kInDriveGreen,
                          size: 24,
                        ),
                      ),
                      onPressed: () async {
                        await _toggleDarkMode();
                        // إظهار رسالة تأكيد
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                _isDarkMode
                                    ? 'تم التبديل إلى الوضع المظلم'
                                    : 'تم التبديل إلى الوضع الفاتح',
                                style: const TextStyle(
                                  fontFamily: 'Tajawal',
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              backgroundColor: kInDriveGreen,
                              duration: const Duration(milliseconds: 1500),
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ],
              )
            : null,
        body: AnimatedSwitcher(
          duration: const Duration(milliseconds: 400),
          child: _pages[_selectedIndex],
        ),
        bottomNavigationBar: _buildCustomNavBar(),
      ),
    );
  }

  Widget _buildCustomNavBar() {
    final navBarColor = _isDarkMode ? kDarkGrey : Colors.white;
    final shadowColor = _isDarkMode ? Colors.black.withValues(alpha: 0.3) : Colors.grey.withValues(alpha: 0.2);

    return Container(
      decoration: BoxDecoration(
        color: navBarColor,
        boxShadow: [
          BoxShadow(
            color: shadowColor,
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomAppBar(
        color: Colors.transparent,
        elevation: 0,
        child: SizedBox(
          height: 70,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildNavItem(icon: Icons.home, label: 'الرئيسية', index: 0),
              _buildNavItem(icon: Icons.medication, label: 'الأدوية', index: 1),
              _buildNavItem(
                icon: Icons.add_circle_outline,
                label: 'طلب دواء',
                index: 2,
              ),
              _buildNavItem(icon: Icons.local_offer, label: 'العروض', index: 3),
              _buildNavItem(
                icon: Icons.chat_bubble_outline,
                label: 'الدردشة',
                index: 4,
              ),
              _buildNavItem(
                icon: Icons.delivery_dining,
                label: 'الطلبات',
                index: 5,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
  }) {
    final isSelected = _selectedIndex == index;
    final inactiveColor = _isDarkMode ? Colors.grey[400]! : kInactiveGrey;

    return Flexible(
      child: InkWell(
        onTap: () => _onTabSelected(index),
        borderRadius: BorderRadius.circular(30),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: isSelected ? kInDriveGreen : inactiveColor,
                size: 24,
              ),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? kInDriveGreen : inactiveColor,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontFamily: 'Poppins',
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// صفحات التابز الحقيقية:
class MainHomeTab extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  final void Function(int) onQuickActionTap;
  final VoidCallback onProfileTap;
  final ScrollController? scrollController;
  const MainHomeTab({
    super.key,
    required this.isDark,
    required this.onToggleDarkMode,
    required this.onQuickActionTap,
    required this.onProfileTap,
    this.scrollController,
  });

  @override
  State<MainHomeTab> createState() => _MainHomeTabState();
}

class _MainHomeTabState extends State<MainHomeTab> {
  File? _profileImage;
  String _userName = 'اسم المستخدم';
  String _userEmail = '';
  String _userAddress = '';
  String _userGovernorate = '';

  @override
  void initState() {
    super.initState();
    _loadProfileImage();
    _loadUserData();
  }

  Future<void> _loadProfileImage() async {
    final prefs = await SharedPreferences.getInstance();
    final path = prefs.getString('profile_image_path');
    if (path != null && File(path).existsSync()) {
      setState(() {
        _profileImage = File(path);
      });
    } else {
      setState(() {
        _profileImage = null;
      });
    }
  }

  Future<void> _loadUserName() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _userName = prefs.getString('profile_name') ?? 'اسم المستخدم';
    });
  }

  void _loadUserData() async {
    try {
      final userName = await UserService.getUserName();
      final userEmail = await UserService.getUserEmail();
      final userAddress = await UserService.getUserAddress();
      final userGovernorate = await UserService.getUserGovernorate();

      if (mounted) {
        setState(() {
          _userName = userName;
          _userEmail = userEmail;
          _userAddress = userAddress;
          _userGovernorate = userGovernorate;
        });
      }
    } catch (e) {
      print('Error loading user data: $e');
      // في حالة الخطأ، استخدم البيانات المحفوظة محلياً
      _loadUserName();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _loadProfileImage();
    _loadUserName();
  }

  // بيانات الطلبات الأخيرة الوهمية
  List<Map<String, String>> get _recentOrders => [
    {
      'pharmacy': 'صيدلية النور',
      'status': 'تم التسليم',
      'time': 'منذ 10 دقائق',
    },
    {
      'pharmacy': 'صيدلية الشفاء',
      'status': 'جار التوصيل',
      'time': 'منذ 30 دقيقة',
    },
    {'pharmacy': 'صيدلية الأمل', 'status': 'ملغي', 'time': 'منذ يوم'},
  ];

  @override
  Widget build(BuildContext context) {
    final bgColor = widget.isDark ? kDarkGrey : Colors.white;
    final textColor = widget.isDark ? Colors.white : kDarkGrey;
    final Size size = MediaQuery.of(context).size;
    return Stack(
      children: [
        // خلفية موجية شفافة
        Positioned(
          top: -80,
          left: -60,
          child: Opacity(
            opacity: 0.18,
            child: SvgPicture.asset(
              'assets/images/pharmacy_welcome.svg',
              width: size.width * 0.7,
              height: 220,
            ),
          ),
        ),
        Positioned(
          bottom: -60,
          right: -60,
          child: Container(
            width: 180,
            height: 180,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [kInDriveGreen.withValues(alpha: 0.13), Colors.transparent],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
        ),
        // المحتوى الرئيسي
        SingleChildScrollView(
          controller: widget.scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 32),
              // هيدر ترحيبي مع زر ملف شخصي
              Stack(
                children: [
                  Container(
                    width: double.infinity,
                    height: 180,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          kInDriveGreen.withValues(alpha: 0.85),
                          kDarkGrey.withValues(alpha: 0.95),
                        ],
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                      ),
                      borderRadius: BorderRadius.circular(32),
                    ),
                    child: Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(18.0),
                          child: SvgPicture.asset(
                            'assets/images/pharmacy_welcome.svg',
                            width: 90,
                            height: 90,
                          ),
                        ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'مرحباً بك، $_userName',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'Poppins',
                                ),
                              ),
                              const SizedBox(height: 4),
                              if (_userGovernorate.isNotEmpty)
                                Text(
                                  _userGovernorate,
                                  style: TextStyle(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontSize: 14,
                                    fontFamily: 'Poppins',
                                  ),
                                ),
                              const SizedBox(height: 8),
                              Text(
                                'اطلب أدويتك وتابع طلباتك بسهولة',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.85),
                                  fontSize: 15,
                                  fontFamily: 'Poppins',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // زر ملف شخصي عائم مع صورة البروفايل
                  Positioned(
                    top: 12,
                    right: 12,
                    child: GestureDetector(
                      onTap: () async {
                        await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => ProfileTab(
                              isDark: widget.isDark,
                              onToggleDarkMode: widget.onToggleDarkMode,
                            ),
                          ),
                        );
                        _loadProfileImage(); // تحديث الصورة بعد العودة
                      },
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: kInDriveGreen.withValues(alpha: 0.18),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                          border: Border.all(color: kInDriveGreen, width: 2),
                        ),
                        child: _profileImage != null
                            ? ClipOval(
                                child: Image.file(
                                  _profileImage!,
                                  width: 48,
                                  height: 48,
                                  fit: BoxFit.cover,
                                ),
                              )
                            : const Icon(
                                Icons.person,
                                color: kInDriveGreen,
                                size: 28,
                              ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 28),

              // 2. الخدمات السريعة بشكل أفقي احترافي
              Text(
                'الخدمات السريعة',
                style: TextStyle(
                  color: textColor,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Poppins',
                ),
              ),
              const SizedBox(height: 14),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _QuickActionCircle(
                    icon: Icons.add_circle_outline,
                    label: 'طلب دواء',
                    onTap: () => widget.onQuickActionTap(2),
                    isDark: widget.isDark,
                    color: kInDriveGreen,
                  ),
                  _QuickActionCircle(
                    icon: Icons.local_offer,
                    label: 'العروض',
                    onTap: () => widget.onQuickActionTap(3),
                    isDark: widget.isDark,
                    color: Colors.orangeAccent,
                  ),
                  _QuickActionCircle(
                    icon: Icons.medication,
                    label: 'الأدوية',
                    onTap: () => widget.onQuickActionTap(1),
                    isDark: widget.isDark,
                    color: Colors.blueAccent,
                  ),
                  _QuickActionCircle(
                    icon: Icons.chat_bubble_outline,
                    label: 'الدردشة',
                    onTap: () => widget.onQuickActionTap(4),
                    isDark: widget.isDark,
                    color: Colors.purpleAccent,
                  ),
                ],
              ),
              const SizedBox(height: 28),
              // 3. قسم العروض بشكل كارت أفقي جذاب
              Text(
                'عروض اليوم',
                style: TextStyle(
                  color: textColor,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Poppins',
                ),
              ),
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(18),
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(22),
                  gradient: LinearGradient(
                    colors: [
                      Colors.orangeAccent.withValues(alpha: 0.85),
                      Colors.white.withValues(alpha: 0.85),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.orangeAccent.withValues(alpha: 0.13),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(Icons.local_offer, color: Colors.orange, size: 36),
                    const SizedBox(width: 18),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'خصم 10% على جميع الطلبات اليوم!',
                            style: TextStyle(
                              color: Colors.deepOrange,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              fontFamily: 'Poppins',
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'سارع بالاستفادة من العرض قبل انتهاء اليوم.',
                            style: TextStyle(
                              color: Colors.black87,
                              fontSize: 13,
                              fontFamily: 'Poppins',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 28),
              // 4. الطلبات الأخيرة بشكل احترافي
              Text(
                'الطلبات الأخيرة',
                style: TextStyle(
                  color: textColor,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Poppins',
                ),
              ),
              const SizedBox(height: 14),
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: _recentOrders.length,
                itemBuilder: (context, i) {
                  final order = _recentOrders[i];
                  Color statusColor;
                  IconData statusIcon;
                  switch (order['status']) {
                    case 'تم التسليم':
                      statusColor = Colors.green;
                      statusIcon = Icons.check_circle;
                      break;
                    case 'ملغي':
                      statusColor = Colors.red;
                      statusIcon = Icons.cancel;
                      break;
                    default:
                      statusColor = kInDriveGreen;
                      statusIcon = Icons.delivery_dining;
                  }
                  return Container(
                    margin: const EdgeInsets.only(bottom: 14),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: bgColor.withValues(alpha: widget.isDark ? 0.28 : 0.38),
                      borderRadius: BorderRadius.circular(18),
                      border: Border.all(
                        color: kInDriveGreen.withValues(alpha: 0.09),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: kInDriveGreen.withValues(alpha: 0.07),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: kInDriveGreen.withValues(alpha: 0.12),
                          child: Icon(
                            Icons.local_pharmacy,
                            color: kInDriveGreen,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                order['pharmacy']!,
                                style: TextStyle(
                                  fontFamily: 'Poppins',
                                  fontWeight: FontWeight.bold,
                                  color: textColor,
                                  fontSize: 15,
                                ),
                              ),
                              Text(
                                order['status']!,
                                style: TextStyle(
                                  color: statusColor,
                                  fontFamily: 'Poppins',
                                  fontSize: 12.5,
                                ),
                              ),
                              Text(
                                order['time']!,
                                style: TextStyle(
                                  color: textColor.withValues(alpha: 0.6),
                                  fontSize: 11.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (order['status'] == 'جار التوصيل') ...[
                          Icon(statusIcon, color: statusColor, size: 26),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: kInDriveGreen,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 7,
                              ),
                              textStyle: const TextStyle(
                                fontFamily: 'Poppins',
                                fontSize: 12,
                              ),
                              elevation: 0,
                            ),
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (_) => OrderTrackingScreen(
                                    isDark: widget.isDark,
                                    onToggleDarkMode: widget.onToggleDarkMode,
                                  ),
                                ),
                              );
                            },
                            child: const Text('تتبع'),
                          ),
                        ] else ...[
                          const SizedBox(width: 8),
                          Icon(statusIcon, color: statusColor, size: 26),
                        ],
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _QuickActionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  final Color textColor;
  final Color bgColor;
  final bool modern;
  final bool glass;
  final bool isDark;
  final EdgeInsets padding;
  final double iconSize;
  final double titleSize;
  final double subtitleSize;
  const _QuickActionCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
    required this.textColor,
    required this.bgColor,
    this.modern = false,
    this.glass = false,
    this.isDark = false,
    this.padding = const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
    this.iconSize = 32,
    this.titleSize = 16.5,
    this.subtitleSize = 12.5,
  });
  @override
  Widget build(BuildContext context) {
    if (modern && glass) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(24),
            onTap: onTap,
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 14, sigmaY: 14),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 350),
                curve: Curves.easeOutCubic,
                padding: padding,
                decoration: BoxDecoration(
                  color: bgColor.withValues(alpha: isDark ? 0.28 : 0.38),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: kInDriveGreen.withValues(alpha: 0.09),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(15),
                      decoration: BoxDecoration(
                        color: kInDriveGreen.withValues(alpha: 0.13),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(icon, size: iconSize, color: kInDriveGreen),
                    ),
                    const SizedBox(height: 15),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: titleSize,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                        fontFamily: 'Poppins',
                        letterSpacing: 0.2,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 5),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: subtitleSize,
                        color: textColor.withValues(alpha: 0.7),
                        fontFamily: 'Poppins',
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    }
    // الشكل التقليدي القديم
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(30),
      child: Container(
        padding: padding,
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: kInDriveGreen.withValues(alpha: 0.07),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: kInDriveGreen.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, size: iconSize, color: kInDriveGreen),
            ),
            const SizedBox(height: 15),
            Text(
              title,
              style: TextStyle(
                fontSize: titleSize,
                fontWeight: FontWeight.bold,
                color: textColor,
                fontFamily: 'Poppins',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 5),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: subtitleSize,
                color: textColor.withValues(alpha: 0.7),
                fontFamily: 'Poppins',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Widget جديد للخدمات السريعة بشكل دائري احترافي
class _QuickActionCircle extends StatefulWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final bool isDark;
  final Color color;
  const _QuickActionCircle({
    required this.icon,
    required this.label,
    required this.onTap,
    required this.isDark,
    required this.color,
  });
  @override
  State<_QuickActionCircle> createState() => _QuickActionCircleState();
}

class _QuickActionCircleState extends State<_QuickActionCircle> {
  bool _pressed = false;
  @override
  Widget build(BuildContext context) {
    final bg = widget.isDark
        ? Colors.white.withValues(alpha: 0.08)
        : Colors.white.withValues(alpha: 0.55);
    return Expanded(
      child: GestureDetector(
        onTapDown: (_) => setState(() => _pressed = true),
        onTapUp: (_) => setState(() => _pressed = false),
        onTapCancel: () => setState(() => _pressed = false),
        onTap: widget.onTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 120),
          curve: Curves.easeOut,
          margin: const EdgeInsets.symmetric(horizontal: 3),
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: bg,
            borderRadius: BorderRadius.circular(50),
            boxShadow: [
              BoxShadow(
                color: widget.color.withValues(alpha: _pressed ? 0.18 : 0.10),
                blurRadius: _pressed ? 18 : 10,
                offset: const Offset(0, 4),
              ),
            ],
            border: Border.all(
              color: widget.color.withValues(alpha: 0.13),
              width: 1.5,
            ),
            // Glassmorphism
            backgroundBlendMode: BlendMode.overlay,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      widget.color.withValues(alpha: 0.18),
                      widget.color.withValues(alpha: 0.07),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Icon(widget.icon, color: widget.color, size: 28),
              ),
              const SizedBox(height: 8),
              Text(
                widget.label,
                style: TextStyle(
                  fontSize: 13.5,
                  fontWeight: FontWeight.bold,
                  color: widget.isDark ? Colors.white : Colors.black87,
                  fontFamily: 'Poppins',
                  letterSpacing: 0.1,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MedicinesTab extends StatelessWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  const MedicinesTab({
    super.key,
    required this.isDark,
    required this.onToggleDarkMode,
  });
  @override
  Widget build(BuildContext context) {
    // محتوى الأدوية
    return Center(child: Text('الأدوية', style: TextStyle(fontSize: 22)));
  }
}
