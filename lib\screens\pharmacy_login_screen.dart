import 'package:flutter/material.dart';
import 'pharmacy_register_screen.dart';
import 'home_screen_pharmacy.dart';

const Color kInDriveGreen = Color(0xFF00BF63);
const Color kDarkGrey = Color(0xFF1B1B1B);

class PharmacyLoginScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  const PharmacyLoginScreen({
    super.key,
    this.isDark = false,
    required this.onToggleDarkMode,
  });

  @override
  State<PharmacyLoginScreen> createState() => _PharmacyLoginScreenState();
}

class _PharmacyLoginScreenState extends State<PharmacyLoginScreen> {
  final _emailOrPhoneController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _rememberMe = false;

  @override
  Widget build(BuildContext context) {
    final bgColor = widget.isDark ? kDarkGrey : Colors.white;
    final textColor = widget.isDark ? Colors.white : kDark<PERSON>rey;

    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        iconTheme: IconThemeData(color: kInDriveGreen),
        title: Text(
          'تسجيل دخول الصيدلية',
          style: TextStyle(
            color: textColor,
            fontFamily: 'Poppins',
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              widget.isDark ? Icons.light_mode : Icons.dark_mode,
              color: kInDriveGreen,
            ),
            onPressed: widget.onToggleDarkMode,
          ),
        ],
      ),
      body: Center(
        child: SingleChildScrollView(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 24),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: widget.isDark ? kDarkGrey : Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: kInDriveGreen.withValues(alpha: 0.07),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.local_pharmacy, size: 60, color: kInDriveGreen),
                const SizedBox(height: 16),
                Text(
                  'تسجيل دخول الصيدلية',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: textColor,
                    fontFamily: 'Poppins',
                  ),
                ),
                const SizedBox(height: 24),
                TextField(
                  controller: _emailOrPhoneController,
                  style: TextStyle(color: textColor, fontFamily: 'Poppins'),
                  decoration: InputDecoration(
                    labelText: 'البريد الإلكتروني أو رقم الهاتف',
                    labelStyle: TextStyle(color: textColor),
                    prefixIcon: Icon(Icons.person, color: kInDriveGreen),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    filled: true,
                    fillColor: widget.isDark ? kDarkGrey : Colors.grey[50],
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _passwordController,
                  obscureText: true,
                  style: TextStyle(color: textColor, fontFamily: 'Poppins'),
                  decoration: InputDecoration(
                    labelText: 'كلمة المرور',
                    labelStyle: TextStyle(color: textColor),
                    prefixIcon: Icon(Icons.lock, color: kInDriveGreen),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    filled: true,
                    fillColor: widget.isDark ? kDarkGrey : Colors.grey[50],
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Checkbox(
                      value: _rememberMe,
                      onChanged: (v) => setState(() => _rememberMe = v!),
                      activeColor: kInDriveGreen,
                    ),
                    Text(
                      'تذكرني',
                      style: TextStyle(color: textColor, fontFamily: 'Poppins'),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kInDriveGreen,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      textStyle: const TextStyle(
                        fontSize: 18,
                        fontFamily: 'Poppins',
                      ),
                      elevation: 4,
                    ),
                    onPressed: () {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (_) => PharmacyOrdersScreen(
                            isDark: widget.isDark,
                            onToggleDarkMode: widget.onToggleDarkMode,
                            onLogout: () {
                              Navigator.pushReplacementNamed(context, '/welcome');
                            },
                          ),
                        ),
                      );
                    },
                    child: const Text('تسجيل الدخول'),
                  ),
                ),
                const SizedBox(height: 12),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) =>
                            PharmacyRegisterScreen(isDark: widget.isDark),
                      ),
                    );
                  },
                  child: Text(
                    'ليس لديك حساب؟ إنشاء حساب',
                    style: TextStyle(
                      color: kInDriveGreen,
                      fontFamily: 'Poppins',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
