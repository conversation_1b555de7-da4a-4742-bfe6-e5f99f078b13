class AppConfig {
  // إعدادات الخادم
  static const String _localhost = 'localhost';

  // 🔧 غير هذا IP إلى IP الكمبيوتر الخاص بك
  // لمعرفة IP الخاص بك: افتح Command Prompt واكتب ipconfig
  // ابحث عن IPv4 Address في قسم Wireless LAN adapter Wi-Fi
  static const String _localIP = '************';

  static const String _port = '3000';

  // تحديد ما إذا كان التطبيق يعمل على المحاكي أم الهاتف الحقيقي
  static bool get isRunningOnDevice {
    // للتطبيق المنزل (APK) دائماً true
    return true;
  }
  
  // العنوان الأساسي للـ API
  static String get baseUrl {
    final host = isRunningOnDevice ? _localIP : _localhost;
    return 'http://$host:$_port/api';
  }
  
  // إعدادات الشبكة
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  
  // إعدادات التطبيق
  static const String appName = 'صيدلية التوصيل';
  static const String appVersion = '1.0.0';
  
  // مفاتيح SharedPreferences
  static const String keyAccessToken = 'access_token';
  static const String keyRefreshToken = 'refresh_token';
  static const String keyUserData = 'user_data';
  static const String keyDarkMode = 'user_dark_mode';
  static const String keyIsAuthenticated = 'is_authenticated';
  
  // رسائل الخطأ
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String serverErrorMessage = 'خطأ في الخادم';
  static const String timeoutErrorMessage = 'انتهت مهلة الاتصال';
  static const String unknownErrorMessage = 'حدث خطأ غير معروف';
  
  // معلومات التشخيص
  static void printNetworkInfo() {
    print('=== معلومات الشبكة ===');
    print('Base URL: $baseUrl');
    print('Running on device: $isRunningOnDevice');
    print('Local IP: $_localIP');
    print('Port: $_port');
    print('====================');
  }
}
