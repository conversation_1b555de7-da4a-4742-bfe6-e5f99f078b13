import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class UserService {
  static const String _userKey = 'user_data';
  static const String _tokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';

  // حفظ بيانات المستخدم
  static Future<void> saveUserData(Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, json.encode(userData));
  }

  // حفظ التوكن
  static Future<void> saveTokens(String accessToken, String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, accessToken);
    await prefs.setString(_refreshTokenKey, refreshToken);
  }

  // الحصول على بيانات المستخدم
  static Future<Map<String, dynamic>?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(_userKey);
    if (userDataString != null) {
      return json.decode(userDataString);
    }
    return null;
  }

  // الحصول على التوكن
  static Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  // الحصول على refresh token
  static Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }

  // مسح بيانات المستخدم (تسجيل الخروج)
  static Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
    await prefs.remove(_tokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.setBool('is_authenticated', false); // إضافة هذا السطر
  }

  /// التحقق من حالة تسجيل الدخول
  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    final isAuthenticated = prefs.getBool('is_authenticated') ?? false;
    final hasToken = await getAccessToken() != null;
    final hasUserData = await getUserData() != null;

    // المستخدم مسجل دخول إذا كان لديه token وبيانات مستخدم
    return isAuthenticated && hasToken && hasUserData;
  }

  /// تسجيل دخول المستخدم (حفظ حالة تسجيل الدخول)
  static Future<void> setLoggedIn(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('is_authenticated', value);
  }

  // الحصول على اسم المستخدم
  static Future<String> getUserName() async {
    final userData = await getUserData();
    return userData?['name'] ?? 'المستخدم';
  }

  // الحصول على إيميل المستخدم
  static Future<String> getUserEmail() async {
    final userData = await getUserData();
    return userData?['email'] ?? '';
  }

  // الحصول على رقم هاتف المستخدم
  static Future<String> getUserPhone() async {
    final userData = await getUserData();
    return userData?['phone'] ?? '';
  }

  // الحصول على عنوان المستخدم
  static Future<String> getUserAddress() async {
    final userData = await getUserData();
    return userData?['address'] ?? '';
  }

  // الحصول على محافظة المستخدم
  static Future<String> getUserGovernorate() async {
    final userData = await getUserData();
    return userData?['governorate'] ?? '';
  }

  // الحصول على حالة تأكيد الإيميل
  static Future<bool> isEmailVerified() async {
    final userData = await getUserData();
    return userData?['isEmailVerified'] ?? false;
  }

  // الحصول على تاريخ آخر تسجيل دخول
  static Future<String> getLastLogin() async {
    final userData = await getUserData();
    final lastLogin = userData?['lastLogin'];
    if (lastLogin != null) {
      final date = DateTime.parse(lastLogin);
      return '${date.day}/${date.month}/${date.year}';
    }
    return '';
  }

  // الحصول على تاريخ إنشاء الحساب
  static Future<String> getCreatedAt() async {
    final userData = await getUserData();
    final createdAt = userData?['createdAt'];
    if (createdAt != null) {
      final date = DateTime.parse(createdAt);
      return '${date.day}/${date.month}/${date.year}';
    }
    return '';
  }

  // تحديث بيانات المستخدم
  static Future<void> updateUserData(Map<String, dynamic> newData) async {
    final currentData = await getUserData();
    if (currentData != null) {
      currentData.addAll(newData);
      await saveUserData(currentData);
    }
  }

  // الحصول على الصورة الشخصية
  static Future<String?> getProfileImage() async {
    final userData = await getUserData();
    return userData?['profileImage'];
  }

  // تحديث الصورة الشخصية
  static Future<void> updateProfileImage(String imagePath) async {
    await updateUserData({'profileImage': imagePath});
  }
}
