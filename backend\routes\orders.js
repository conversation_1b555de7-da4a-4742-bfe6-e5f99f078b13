const express = require('express');
const Order = require('../models/Order');
const Medicine = require('../models/Medicine');
const Pharmacy = require('../models/Pharmacy');
const Inventory = require('../models/Inventory');
const { protect, requireVerification, authorize } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

const router = express.Router();

// @desc    Get user orders
// @route   GET /api/orders
// @access  Private
router.get('/', protect, requireVerification, async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;

    const query = { user: req.user.id };
    if (status) query.status = status;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const orders = await Order.find(query)
      .populate('pharmacy', 'name address phone')
      .populate('items.medicine', 'name manufacturer form strength')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Order.countDocuments(query);

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الطلبات'
    });
  }
});

// @desc    Get order by ID
// @route   GET /api/orders/:id
// @access  Private
router.get('/:id', protect, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('user', 'name email phone')
      .populate('pharmacy', 'name address phone services')
      .populate('items.medicine', 'name manufacturer form strength images');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    // Check if user owns the order or is pharmacy/admin
    if (req.user.role === 'user' && order.user._id.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بعرض هذا الطلب'
      });
    }

    if (req.user.role === 'pharmacy' && order.pharmacy._id.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بعرض هذا الطلب'
      });
    }

    res.json({
      success: true,
      data: { order }
    });
  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الطلب'
    });
  }
});

// @desc    Create new order
// @route   POST /api/orders
// @access  Private
router.post('/', protect, requireVerification, [
  body('pharmacy').isMongoId().withMessage('معرف الصيدلية غير صحيح'),
  body('items').isArray({ min: 1 }).withMessage('يجب أن يحتوي الطلب على دواء واحد على الأقل'),
  body('items.*.medicine').isMongoId().withMessage('معرف الدواء غير صحيح'),
  body('items.*.quantity').isInt({ min: 1 }).withMessage('الكمية يجب أن تكون رقم صحيح أكبر من صفر'),
  body('deliveryAddress.street').notEmpty().withMessage('عنوان الشارع مطلوب'),
  body('deliveryAddress.city').notEmpty().withMessage('المدينة مطلوبة'),
  body('deliveryAddress.governorate').notEmpty().withMessage('المحافظة مطلوبة'),
  body('contactInfo.phone').matches(/^[0-9]{10,15}$/).withMessage('رقم الهاتف غير صحيح'),
  body('paymentMethod').isIn(['cash', 'card', 'wallet', 'insurance']).withMessage('طريقة الدفع غير صحيحة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const {
      pharmacy: pharmacyId,
      items,
      prescription,
      deliveryAddress,
      contactInfo,
      paymentMethod,
      urgency = 'normal',
      notes
    } = req.body;

    // Verify pharmacy exists and is active
    const pharmacy = await Pharmacy.findById(pharmacyId);
    if (!pharmacy || !pharmacy.isActive || !pharmacy.isVerified) {
      return res.status(400).json({
        success: false,
        message: 'الصيدلية غير متاحة'
      });
    }

    // Verify medicines and get pricing
    const orderItems = [];
    let subtotal = 0;

    for (const item of items) {
      const medicine = await Medicine.findById(item.medicine);
      if (!medicine || !medicine.isActive || !medicine.isApproved) {
        return res.status(400).json({
          success: false,
          message: `الدواء غير متاح: ${medicine?.name || 'غير معروف'}`
        });
      }

      // Get inventory information
      const inventory = await Inventory.findOne({
        pharmacy: pharmacyId,
        medicine: item.medicine,
        isActive: true
      });

      if (!inventory || inventory.availableQuantity < item.quantity) {
        return res.status(400).json({
          success: false,
          message: `الكمية المطلوبة غير متوفرة للدواء: ${medicine.name}`
        });
      }

      const unitPrice = inventory.pricing.discountPrice > 0
        ? inventory.pricing.discountPrice
        : inventory.pricing.sellingPrice;

      const totalPrice = unitPrice * item.quantity;

      orderItems.push({
        medicine: item.medicine,
        quantity: item.quantity,
        unitPrice: unitPrice,
        totalPrice: totalPrice,
        notes: item.notes
      });

      subtotal += totalPrice;

      // Reserve stock
      await inventory.reserveStock(item.quantity);
    }

    // Calculate delivery fee
    const deliveryFee = pharmacy.services.delivery.available
      ? (subtotal >= pharmacy.services.delivery.freeDeliveryMinimum
          ? 0
          : pharmacy.services.delivery.fee)
      : 0;

    // Calculate total
    const total = subtotal + deliveryFee;

    // Create order
    const order = await Order.create({
      user: req.user.id,
      pharmacy: pharmacyId,
      items: orderItems,
      prescription,
      pricing: {
        subtotal,
        deliveryFee,
        total
      },
      deliveryAddress,
      contactInfo,
      paymentMethod,
      urgency,
      notes,
      delivery: {
        estimatedTime: new Date(Date.now() + pharmacy.services.delivery.estimatedTime * 60000)
      }
    });

    // Populate order for response
    await order.populate([
      { path: 'pharmacy', select: 'name address phone' },
      { path: 'items.medicine', select: 'name manufacturer form strength' }
    ]);

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الطلب بنجاح',
      data: { order }
    });
  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء الطلب'
    });
  }
});

// @desc    Update order status (Pharmacy only)
// @route   PUT /api/orders/:id/status
// @access  Private/Pharmacy
router.put('/:id/status', protect, authorize('pharmacy'), [
  body('status').isIn(['confirmed', 'preparing', 'ready', 'out_for_delivery', 'delivered', 'cancelled'])
    .withMessage('حالة الطلب غير صحيحة'),
  body('notes').optional().isString().withMessage('الملاحظات يجب أن تكون نص')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const { status, notes, driverInfo } = req.body;

    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    // Check if pharmacy owns the order
    if (order.pharmacy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بتحديث هذا الطلب'
      });
    }

    // Update driver info if provided
    if (driverInfo && status === 'out_for_delivery') {
      order.delivery.driver = driverInfo;
    }

    // Update status
    await order.updateStatus(status, notes, req.user.id, 'Pharmacy');

    // If delivered, update inventory
    if (status === 'delivered') {
      for (const item of order.items) {
        const inventory = await Inventory.findOne({
          pharmacy: req.user.id,
          medicine: item.medicine
        });

        if (inventory) {
          await inventory.addMovement('sale', item.quantity, 'بيع - طلب رقم ' + order.orderNumber, order.orderNumber, req.user.id);
          await inventory.releaseReservedStock(item.quantity);
        }
      }

      order.delivery.actualDeliveryTime = new Date();
    }

    // If cancelled, release reserved stock
    if (status === 'cancelled') {
      for (const item of order.items) {
        const inventory = await Inventory.findOne({
          pharmacy: req.user.id,
          medicine: item.medicine
        });

        if (inventory) {
          await inventory.releaseReservedStock(item.quantity);
        }
      }
    }

    await order.save();

    res.json({
      success: true,
      message: 'تم تحديث حالة الطلب بنجاح',
      data: { order }
    });
  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث حالة الطلب'
    });
  }
});

// @desc    Cancel order
// @route   PUT /api/orders/:id/cancel
// @access  Private
router.put('/:id/cancel', protect, [
  body('reason').notEmpty().withMessage('سبب الإلغاء مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const { reason } = req.body;

    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    // Check permissions
    const canCancel = (req.user.role === 'user' && order.user.toString() === req.user.id) ||
                     (req.user.role === 'pharmacy' && order.pharmacy.toString() === req.user.id) ||
                     req.user.role === 'admin';

    if (!canCancel) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بإلغاء هذا الطلب'
      });
    }

    // Check if order can be cancelled
    if (['delivered', 'cancelled'].includes(order.status)) {
      return res.status(400).json({
        success: false,
        message: 'لا يمكن إلغاء هذا الطلب'
      });
    }

    // Release reserved stock
    if (req.user.role === 'pharmacy' || req.user.role === 'admin') {
      for (const item of order.items) {
        const inventory = await Inventory.findOne({
          pharmacy: order.pharmacy,
          medicine: item.medicine
        });

        if (inventory) {
          await inventory.releaseReservedStock(item.quantity);
        }
      }
    }

    // Cancel order
    await order.cancelOrder(reason, req.user.id, req.user.role === 'user' ? 'User' : req.user.role === 'pharmacy' ? 'Pharmacy' : 'Admin');

    res.json({
      success: true,
      message: 'تم إلغاء الطلب بنجاح',
      data: { order }
    });
  } catch (error) {
    console.error('Cancel order error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إلغاء الطلب'
    });
  }
});

// @desc    Rate order (User only)
// @route   PUT /api/orders/:id/rate
// @access  Private/User
router.put('/:id/rate', protect, authorize('user'), [
  body('pharmacyRating').isInt({ min: 1, max: 5 }).withMessage('تقييم الصيدلية يجب أن يكون بين 1 و 5'),
  body('deliveryRating').isInt({ min: 1, max: 5 }).withMessage('تقييم التوصيل يجب أن يكون بين 1 و 5'),
  body('comment').optional().isString().withMessage('التعليق يجب أن يكون نص')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array().map(error => error.msg)
      });
    }

    const { pharmacyRating, deliveryRating, comment } = req.body;

    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'الطلب غير موجود'
      });
    }

    // Check if user owns the order
    if (order.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بتقييم هذا الطلب'
      });
    }

    // Check if order is delivered
    if (order.status !== 'delivered') {
      return res.status(400).json({
        success: false,
        message: 'يمكن تقييم الطلب فقط بعد التسليم'
      });
    }

    // Check if already rated
    if (order.rating.ratedAt) {
      return res.status(400).json({
        success: false,
        message: 'تم تقييم هذا الطلب بالفعل'
      });
    }

    // Add rating
    await order.addRating(pharmacyRating, deliveryRating, comment);

    // Update pharmacy rating
    const pharmacy = await Pharmacy.findById(order.pharmacy);
    if (pharmacy) {
      const currentTotal = pharmacy.rating.average * pharmacy.rating.count;
      const newCount = pharmacy.rating.count + 1;
      const newAverage = (currentTotal + pharmacyRating) / newCount;

      pharmacy.rating.average = Math.round(newAverage * 10) / 10;
      pharmacy.rating.count = newCount;
      await pharmacy.save();
    }

    res.json({
      success: true,
      message: 'تم إضافة التقييم بنجاح',
      data: { order }
    });
  } catch (error) {
    console.error('Rate order error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إضافة التقييم'
    });
  }
});

module.exports = router;
