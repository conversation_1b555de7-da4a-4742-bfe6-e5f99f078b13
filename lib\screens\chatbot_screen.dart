import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

class ChatBotScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  const ChatBotScreen({
    super.key,
    required this.isDark,
    required this.onToggleDarkMode,
  });

  @override
  State<ChatBotScreen> createState() => _ChatBotScreenState();
}

class _ChatBotScreenState extends State<ChatBotScreen> {
  final TextEditingController _controller = TextEditingController();
  final List<_ChatMessage> _messages = [];
  bool _isLoading = false;
  static const String _apiKey = 'AIzaSyAF6_6dxATIyrxpn4NHvdlKhndwPe6RAjg';

  Future<void> _sendMessage() async {
    final text = _controller.text.trim();
    if (text.isEmpty) return;
    setState(() {
      _messages.add(_ChatMessage(text: text, isUser: true));
      _isLoading = true;
      _controller.clear();
    });
    final reply = await _getGeminiResponse(text);
    setState(() {
      _messages.add(_ChatMessage(text: reply, isUser: false));
      _isLoading = false;
    });
  }

  Future<String> _getGeminiResponse(String prompt) async {
    try {
      final url = Uri.parse(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$_apiKey',
      );
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'contents': [
            {
              'parts': [
                {
                  'text':
                      'أنت مساعد صيدلي محترف. أجب فقط عن الأسئلة المتعلقة بالعلاج والأدوية والصحة. إذا كان السؤال خارج هذا النطاق، اعتذر للمستخدم.\nسؤال المستخدم: $prompt',
                },
              ],
            },
          ],
        }),
      );
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final text = data['candidates']?[0]?['content']?['parts']?[0]?['text'];
        if (text != null && text is String) {
          return text.trim();
        }
      }
      return 'حدث خطأ في الاتصال بالخدمة. حاول لاحقاً.';
    } catch (e) {
      return 'تعذر الاتصال بالخدمة: $e';
    }
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = widget.isDark ? const Color(0xFF1B1B1B) : Colors.white;
    final textColor = widget.isDark ? Colors.white : const Color(0xFF1B1B1B);
    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        backgroundColor: bgColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF00BF63)),
        title: Text(
          'مساعد العلاج الذكي',
          style: TextStyle(
            color: textColor,
            fontWeight: FontWeight.bold,
            fontFamily: 'Poppins',
          ),
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 18, 16, 6),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                color: widget.isDark ? Colors.white10 : Colors.grey[100],
                borderRadius: BorderRadius.circular(18),
              ),
              child: Text(
                '👋 مرحباً بك في مساعد العلاج الذكي!\nيمكنك سؤالي عن الأدوية، الجرعات، التداخلات الدوائية، النصائح الصحية، أو أي استفسار علاجي.\nيرجى عدم مشاركة بيانات شخصية حساسة.',
                style: TextStyle(
                  color: widget.isDark ? Colors.white70 : Colors.black87,
                  fontFamily: 'Poppins',
                  fontSize: 14.5,
                ),
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              reverse: true,
              itemCount: _messages.length,
              itemBuilder: (context, i) {
                final msg = _messages[_messages.length - 1 - i];
                return Align(
                  alignment: msg.isUser
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 6),
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      color: msg.isUser
                          ? const Color(0xFF00BF63)
                          : (widget.isDark ? Colors.white10 : Colors.grey[200]),
                      borderRadius: BorderRadius.only(
                        topLeft: const Radius.circular(18),
                        topRight: const Radius.circular(18),
                        bottomLeft: Radius.circular(msg.isUser ? 18 : 4),
                        bottomRight: Radius.circular(msg.isUser ? 4 : 18),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.04),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Text(
                      msg.text,
                      style: TextStyle(
                        color: msg.isUser
                            ? Colors.white
                            : (widget.isDark ? Colors.white : Colors.black87),
                        fontFamily: 'Poppins',
                        fontSize: 15.5,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(12.0),
              child: CircularProgressIndicator(color: Color(0xFF00BF63)),
            ),
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 0, 12, 18),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    style: TextStyle(color: textColor, fontFamily: 'Poppins'),
                    decoration: InputDecoration(
                      hintText: 'اسأل عن علاج أو دواء...',
                      hintStyle: TextStyle(
                        color: textColor.withValues(alpha: 0.5),
                        fontFamily: 'Poppins',
                      ),
                      filled: true,
                      fillColor: widget.isDark
                          ? const Color(0xFF232323)
                          : Colors.grey[100],
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(22),
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        vertical: 0,
                        horizontal: 16,
                      ),
                    ),
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.send, color: Color(0xFF00BF63)),
                  onPressed: _isLoading ? null : _sendMessage,
                  iconSize: 28,
                  splashRadius: 24,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ChatMessage {
  final String text;
  final bool isUser;
  _ChatMessage({required this.text, required this.isUser});
}
 