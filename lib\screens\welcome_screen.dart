import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/animation.dart';
import 'package:pharmacy/screens/auth_screen.dart';
import 'package:pharmacy/screens/pharmacy_login_screen.dart';
import 'package:pharmacy/screens/user_login_screen.dart';
import 'package:pharmacy/screens/home_screen.dart';
import 'package:pharmacy/test_api.dart';
// import 'package:pharmacy/utils/localization/my_app_locale.dart';
import 'package:easy_localization/easy_localization.dart';

class WelcomeScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  final void Function(Locale)? onLocaleChanged;

  const WelcomeScreen({
    Key? key,
    required this.isDark,
    required this.onToggleDarkMode,
    this.onLocaleChanged,
  }) : super(key: key);

  @override
  _WelcomeScreenState createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;
  String lang = 'العربية';

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.3, 0.8, curve: Curves.elasticOut),
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _controller,
            curve: const Interval(0.5, 1.0, curve: Curves.easeOut),
          ),
        );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _changeLanguage(String? value) {
    if (value == null) return;
    setState(() {
      lang = value;
    });
    Locale newLocale = value == 'English'
        ? const Locale('en')
        : const Locale('ar');
    EasyLocalization.of(context)!.setLocale(newLocale);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = widget.isDark ? const Color(0xFF121212) : Colors.white;
    final textColor = widget.isDark ? Colors.white : const Color(0xFF121212);
    const primaryColor = Color(0xFF00BF63);
    return Scaffold(
      backgroundColor: bgColor,
      body: SafeArea(
        child: Stack(
          children: [
            // زر اختبار API
            Positioned(
              top: 16,
              right: 16,
              child: FloatingActionButton.small(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const TestApiScreen(),
                    ),
                  );
                },
                backgroundColor: primaryColor,
                child: const Icon(Icons.bug_report, color: Colors.white),
              ),
            ),
            // خلفية متحركة
            Positioned.fill(
              child: AnimatedContainer(
                duration: const Duration(seconds: 1),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: widget.isDark
                        ? [const Color(0xFF121212), const Color(0xFF1E1E1E)]
                        : [Colors.white, const Color(0xFFF5F5F5)],
                  ),
                ),
              ),
            ),

            // عناصر التحكم العلوية
            Positioned(
              top: 16,
              right: 16,
              child: Row(
                children: [
                  // زر اللغة
                  Container(
                    decoration: BoxDecoration(
                      color: widget.isDark ? Colors.white10 : Colors.grey[200],
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: lang,
                        icon: const Icon(Icons.language, size: 20),
                        style: TextStyle(
                          color: textColor,
                          fontFamily: 'Poppins',
                          fontWeight: FontWeight.bold,
                        ),
                        dropdownColor: bgColor,
                        borderRadius: BorderRadius.circular(20),
                        items: [
                          DropdownMenuItem(
                            value: 'العربية',
                            child: Text('arabic'.tr()),
                          ),
                          DropdownMenuItem(
                            value: 'English',
                            child: Text('english'.tr()),
                          ),
                        ],
                        onChanged: _changeLanguage,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // زر الوضع الليلي
                  IconButton(
                    icon: Icon(
                      widget.isDark ? Icons.light_mode : Icons.dark_mode,
                      color: primaryColor,
                    ),
                    onPressed: widget.onToggleDarkMode,
                    tooltip: 'darkMode'.tr(),
                  ),
                ],
              ),
            ),

            // المحتوى الرئيسي
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // شعار متحرك
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: ScaleTransition(
                        scale: _scaleAnimation,
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                primaryColor.withValues(alpha: 0.1),
                                primaryColor.withValues(alpha: 0.05),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            shape: BoxShape.circle,
                          ),
                          padding: const EdgeInsets.all(24),
                          child: SvgPicture.asset(
                            'assets/images/pharmacy_welcome.svg',
                            width: 150,
                            height: 150,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 32),

                    // عنوان التطبيق
                    SlideTransition(
                      position: _slideAnimation,
                      child: ShaderMask(
                        shaderCallback: (Rect bounds) {
                          return LinearGradient(
                            colors: [
                              primaryColor,
                              primaryColor.withValues(alpha: 0.8),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ).createShader(bounds);
                        },
                        child: Text(
                          'appTitle'.tr(),
                          style: TextStyle(
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Tajawal',
                            letterSpacing: 1.2,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // وصف التطبيق
                    SlideTransition(
                      position: _slideAnimation,
                      child: Text(
                        'welcome'.tr(),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 18,
                          color: textColor.withValues(alpha: 0.8),
                          fontFamily: 'Tajawal',
                          height: 1.6,
                        ),
                      ),
                    ),

                    const SizedBox(height: 48),

                    // أزرار الدخول
                    SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        children: [
                          // زر الدخول كمستخدم
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: primaryColor,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 18,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 4,
                                shadowColor: primaryColor.withValues(alpha: 0.3),
                              ),
                              onPressed: _navigateToUserAuth,
                              child: Text(
                                'loginAsUser'.tr(),
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontFamily: 'Tajawal',
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // زر الدخول كصيدلية
                          SizedBox(
                            width: double.infinity,
                            child: OutlinedButton(
                              style: OutlinedButton.styleFrom(
                                foregroundColor: primaryColor,
                                side: BorderSide(color: primaryColor, width: 2),
                                padding: const EdgeInsets.symmetric(
                                  vertical: 18,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              onPressed: _navigateToPharmacyLogin,
                              child: Text(
                                'loginAsPharmacy'.tr(),
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontFamily: 'Tajawal',
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToUserAuth() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const UserLoginScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1, 0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToPharmacyLogin() {
    Navigator.pushReplacement(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            PharmacyLoginScreen(
              isDark: widget.isDark,
              onToggleDarkMode: widget.onToggleDarkMode,
            ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1, 0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }
}
