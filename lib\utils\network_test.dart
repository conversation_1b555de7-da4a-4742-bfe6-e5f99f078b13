import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';

class NetworkTest {
  /// اختبار الاتصال بالخادم
  static Future<Map<String, dynamic>> testConnection() async {
    final result = <String, dynamic>{
      'success': false,
      'message': '',
      'details': <String, dynamic>{},
    };

    try {
      print('🔍 بدء اختبار الاتصال...');
      AppConfig.printNetworkInfo();

      // اختبار ping بسيط
      final pingUrl = '${AppConfig.baseUrl.replaceAll('/api', '')}/health';
      print('📡 اختبار الاتصال مع: $pingUrl');

      final response = await http.get(
        Uri.parse(pingUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(
        AppConfig.connectionTimeout,
        onTimeout: () {
          throw Exception('انتهت مهلة الاتصال');
        },
      );

      result['details']['statusCode'] = response.statusCode;
      result['details']['headers'] = response.headers;
      result['details']['body'] = response.body;

      if (response.statusCode == 200) {
        result['success'] = true;
        result['message'] = 'الاتصال ناجح! ✅';
        print('✅ الاتصال ناجح!');
      } else {
        result['message'] = 'الخادم يرد ولكن برمز خطأ: ${response.statusCode}';
        print('⚠️ الخادم يرد ولكن برمز خطأ: ${response.statusCode}');
      }

    } catch (e) {
      result['message'] = 'فشل الاتصال: ${e.toString()}';
      result['details']['error'] = e.toString();
      
      print('❌ فشل الاتصال: $e');
      
      // تحليل نوع الخطأ
      if (e.toString().contains('SocketException')) {
        result['message'] += '\n\n💡 نصائح لحل المشكلة:\n'
                            '• تأكد من أن الهاتف والكمبيوتر على نفس الشبكة\n'
                            '• تأكد من أن الخادم يعمل على ${AppConfig.baseUrl}\n'
                            '• تحقق من إعدادات الجدار الناري (Firewall)';
      } else if (e.toString().contains('TimeoutException')) {
        result['message'] += '\n\n💡 المشكلة: انتهت مهلة الاتصال\n'
                            '• تحقق من سرعة الإنترنت\n'
                            '• تأكد من أن الخادم يعمل';
      }
    }

    return result;
  }

  /// اختبار تسجيل مستخدم وهمي
  static Future<Map<String, dynamic>> testRegistration() async {
    final result = <String, dynamic>{
      'success': false,
      'message': '',
      'details': <String, dynamic>{},
    };

    try {
      print('🧪 اختبار تسجيل مستخدم وهمي...');

      final testData = {
        'name': 'مستخدم تجريبي',
        'email': 'test_${DateTime.now().millisecondsSinceEpoch}@test.com',
        'phone': '01000000000',
        'password': 'Test123456',
        'address': 'عنوان تجريبي',
        'governorate': 'القاهرة',
      };

      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/auth/register'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(testData),
      ).timeout(AppConfig.connectionTimeout);

      result['details']['statusCode'] = response.statusCode;
      result['details']['body'] = response.body;

      if (response.statusCode == 200 || response.statusCode == 201) {
        result['success'] = true;
        result['message'] = 'اختبار التسجيل ناجح! ✅';
        print('✅ اختبار التسجيل ناجح!');
      } else {
        result['message'] = 'فشل اختبار التسجيل - كود: ${response.statusCode}';
        print('❌ فشل اختبار التسجيل - كود: ${response.statusCode}');
        print('Response: ${response.body}');
      }

    } catch (e) {
      result['message'] = 'خطأ في اختبار التسجيل: ${e.toString()}';
      result['details']['error'] = e.toString();
      print('❌ خطأ في اختبار التسجيل: $e');
    }

    return result;
  }

  /// تشغيل جميع الاختبارات
  static Future<void> runAllTests() async {
    print('🚀 بدء اختبارات الشبكة...\n');

    // اختبار الاتصال
    final connectionTest = await testConnection();
    print('نتيجة اختبار الاتصال: ${connectionTest['message']}\n');

    // إذا نجح الاتصال، اختبر التسجيل
    if (connectionTest['success']) {
      final registrationTest = await testRegistration();
      print('نتيجة اختبار التسجيل: ${registrationTest['message']}\n');
    }

    print('🏁 انتهت الاختبارات');
  }
}
