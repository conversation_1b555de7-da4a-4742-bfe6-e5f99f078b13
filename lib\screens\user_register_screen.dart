import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/api_service.dart';
import '../utils/network_test.dart';
import 'email_verification_screen.dart';

const Color kInDriveGreen = Color(0xFF00BF63);
const Color kDarkGrey = Color(0xFF1B1B1B);

class UserRegisterScreen extends StatefulWidget {
  const UserRegisterScreen({super.key});

  @override
  State<UserRegisterScreen> createState() => _UserRegisterScreenState();
}

class _UserRegisterScreenState extends State<UserRegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _addressController = TextEditingController();

  String _selectedGovernorate = 'القاهرة';
  final List<String> _governorateOptions = [
    'القاهرة',
    'الجيزة',
    'الإسكندرية',
    'الدقهلية',
    'البحر الأحمر',
    'البحيرة',
    'الفيوم',
    'الغربية',
    'الإسماعيلية',
    'المنوفية',
    'المنيا',
    'القليوبية',
    'الوادي الجديد',
    'السويس',
    'أسوان',
    'أسيوط',
    'بني سويف',
    'بورسعيد',
    'دمياط',
    'الشرقية',
    'جنوب سيناء',
    'كفر الشيخ',
    'مطروح',
    'الأقصر',
    'قنا',
    'شمال سيناء',
    'سوهاج',
  ];

  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;
  bool _isDarkMode = false;

  @override
  void initState() {
    super.initState();
    _loadDarkMode();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  Future<void> _loadDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('user_dark_mode') ?? false;
    });
  }

  Future<void> _registerUser() async {
    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00BF63)),
        ),
      ),
    );

    try {
      final apiService = ApiService();

      final response = await apiService.register(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        password: _passwordController.text,
        address: _addressController.text.trim(),
        governorate: _selectedGovernorate,
      );

      // إخفاء مؤشر التحميل
      if (mounted) Navigator.pop(context);

      if (mounted && response['success']) {
        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              response['message'] ?? 'تم إنشاء الحساب بنجاح!',
              style: const TextStyle(fontFamily: 'Tajawal'),
            ),
            backgroundColor: const Color(0xFF00BF63),
            duration: const Duration(seconds: 3),
          ),
        );

        // الانتقال لصفحة التحقق من الكود
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => EmailVerificationScreen(
              email: _emailController.text.trim(),
            ),
          ),
        );
      }
    } catch (e) {
      // إخفاء مؤشر التحميل
      if (mounted) Navigator.pop(context);

      String errorMessage = 'حدث خطأ أثناء إنشاء الحساب';

      print('Registration error: $e'); // للتشخيص

      if (e is ApiException) {
        errorMessage = e.message;
      }

      // عرض رسالة الخطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            errorMessage,
            style: const TextStyle(fontFamily: 'Tajawal'),
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _showEmailVerificationDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text(
          'تأكيد البريد الإلكتروني',
          style: TextStyle(fontFamily: 'Tajawal'),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.email,
              size: 64,
              color: Color(0xFF00BF63),
            ),
            const SizedBox(height: 16),
            Text(
              'تم إرسال رابط التأكيد إلى بريدك الإلكتروني:\n${_emailController.text}',
              textAlign: TextAlign.center,
              style: const TextStyle(fontFamily: 'Tajawal'),
            ),
            const SizedBox(height: 16),
            const Text(
              'يرجى فتح بريدك الإلكتروني والنقر على رابط التأكيد لإكمال التسجيل.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: 'Tajawal',
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // إغلاق الحوار
              Navigator.pop(context); // العودة لصفحة تسجيل الدخول
            },
            child: const Text(
              'حسناً',
              style: TextStyle(
                fontFamily: 'Tajawal',
                color: Color(0xFF00BF63),
              ),
            ),
          ),
          TextButton(
            onPressed: () => _resendVerificationEmail(),
            child: const Text(
              'إعادة إرسال',
              style: TextStyle(
                fontFamily: 'Tajawal',
                color: Colors.orange,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _resendVerificationEmail() async {
    try {
      final apiService = ApiService();

      final response = await apiService.resendVerificationCode(_emailController.text.trim());

      if (response['success']) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              response['message'] ?? 'تم إعادة إرسال رابط التأكيد',
              style: const TextStyle(fontFamily: 'Tajawal'),
            ),
            backgroundColor: const Color(0xFF00BF63),
          ),
        );
      }
    } catch (e) {
      String errorMessage = 'فشل في إعادة إرسال رابط التأكيد';

      if (e is ApiException) {
        errorMessage = e.message;
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            errorMessage,
            style: const TextStyle(fontFamily: 'Tajawal'),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = _isDarkMode ? kDarkGrey : const Color(0xFFF8F9FA);
    final cardColor = _isDarkMode ? Colors.grey[800]! : Colors.white;
    final textColor = _isDarkMode ? Colors.white : kDarkGrey;

    return Scaffold(
      backgroundColor: bgColor,
      appBar: AppBar(
        title: const Text(
          'إنشاء حساب جديد',
          style: TextStyle(
            fontFamily: 'Tajawal',
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: kInDriveGreen,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          // زر اختبار الشبكة
          IconButton(
            icon: const Icon(Icons.network_check),
            tooltip: 'اختبار الاتصال',
            onPressed: () async {
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => AlertDialog(
                  title: const Text('اختبار الاتصال'),
                  content: const Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('جاري اختبار الاتصال بالخادم...'),
                    ],
                  ),
                ),
              );

              final result = await NetworkTest.testConnection();

              if (mounted) {
                Navigator.pop(context);
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text(
                      result['success'] ? 'نجح الاختبار ✅' : 'فشل الاختبار ❌',
                    ),
                    content: Text(result['message']),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('موافق'),
                      ),
                    ],
                  ),
                );
              }
            },
          ),
          IconButton(
            icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: () async {
              setState(() {
                _isDarkMode = !_isDarkMode;
              });
              final prefs = await SharedPreferences.getInstance();
              await prefs.setBool('user_dark_mode', _isDarkMode);
            },
          ),
        ],
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: cardColor,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  // Header Section
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: const Color(0xFF00BF63).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: const Icon(
                      Icons.person_add,
                      size: 50,
                      color: Color(0xFF00BF63),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'إنشاء حساب جديد',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'أدخل بياناتك لإنشاء حساب جديد',
                    style: TextStyle(
                      fontSize: 14,
                      color: textColor.withValues(alpha: 0.7),
                      fontFamily: 'Tajawal',
                    ),
                  ),
                  const SizedBox(height: 24),
                  _buildTextField(
                    _nameController,
                    'الاسم الكامل',
                    Icons.person,
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _emailController,
                    'البريد الإلكتروني',
                    Icons.email,
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _phoneController,
                    'رقم الهاتف',
                    Icons.phone,
                    keyboardType: TextInputType.phone,
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _addressController,
                    'العنوان التفصيلي',
                    Icons.location_on,
                    maxLines: 3,
                  ),
                  const SizedBox(height: 12),
                  _buildGovernorateDropdown(),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _passwordController,
                    'كلمة المرور',
                    Icons.lock,
                    isPassword: true,
                  ),
                  const SizedBox(height: 12),
                  _buildTextField(
                    _confirmPasswordController,
                    'تأكيد كلمة المرور',
                    Icons.lock_outline,
                    isPassword: true,
                  ),
                  const SizedBox(height: 24),
                  // Register Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF00BF63),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        elevation: 3,
                        shadowColor: const Color(0xFF00BF63).withValues(alpha: 0.3),
                      ),
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          _registerUser();
                        } else {
                          // عرض رسالة تنبيه للمتطلبات
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'يرجى التأكد من:\n'
                                '• الاسم: أحرف عربية أو إنجليزية فقط (2-100 حرف)\n'
                                '• البريد الإلكتروني: صيغة صحيحة\n'
                                '• رقم الهاتف: أرقام فقط (10-15 رقم)\n'
                                '• كلمة المرور: أحرف وأرقام (6 أحرف على الأقل)\n'
                                '• العنوان: 10-500 حرف',
                                style: TextStyle(fontFamily: 'Tajawal'),
                              ),
                              backgroundColor: Colors.orange,
                              duration: Duration(seconds: 5),
                            ),
                          );
                        }
                      },
                      child: const Text(
                        'إنشاء الحساب',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Login Link
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'لديك حساب بالفعل؟ ',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontFamily: 'Tajawal',
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: const Text(
                          'تسجيل الدخول',
                          style: TextStyle(
                            color: Color(0xFF00BF63),
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Tajawal',
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    String label,
    IconData icon, {
    bool isPassword = false,
    TextInputType? keyboardType,
    int maxLines = 1,
  }) {
    final textColor = _isDarkMode ? Colors.white : kDarkGrey;

    return TextFormField(
      controller: controller,
      obscureText: isPassword,
      keyboardType: keyboardType,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: kInDriveGreen),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: kInDriveGreen),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: kInDriveGreen, width: 2),
        ),
        filled: true,
        fillColor: _isDarkMode ? Colors.grey[700] : Colors.grey[50],
        labelStyle: const TextStyle(
          fontFamily: 'Tajawal',
          color: kInDriveGreen,
        ),
      ),
      style: TextStyle(
        fontFamily: 'Tajawal',
        color: textColor,
      ),
      validator: (v) {
        if (v == null || v.isEmpty) {
          return 'هذا الحقل مطلوب';
        }

        // التحقق من الاسم
        if (label == 'الاسم الكامل') {
          if (v.length < 2 || v.length > 100) {
            return 'الاسم يجب أن يكون بين 2 و 100 حرف';
          }
          if (!RegExp(r'^[a-zA-Zأ-ي\s]+$').hasMatch(v)) {
            return 'الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط';
          }
        }

        // التحقق من البريد الإلكتروني
        if (label == 'البريد الإلكتروني') {
          if (!RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$').hasMatch(v)) {
            return 'يرجى إدخال بريد إلكتروني صحيح';
          }
        }

        // التحقق من رقم الهاتف
        if (label == 'رقم الهاتف') {
          if (!RegExp(r'^[0-9]{10,15}$').hasMatch(v)) {
            return 'رقم الهاتف يجب أن يكون بين 10 و 15 رقم';
          }
        }

        // التحقق من كلمة المرور
        if (label == 'كلمة المرور') {
          if (v.length < 6) {
            return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
          }
          if (!RegExp(r'^(?=.*[a-zA-Z])(?=.*[0-9])').hasMatch(v)) {
            return 'كلمة المرور يجب أن تحتوي على أحرف وأرقام';
          }
        }

        // التحقق من تأكيد كلمة المرور
        if (label == 'تأكيد كلمة المرور' && v != _passwordController.text) {
          return 'كلمة المرور غير متطابقة';
        }

        // التحقق من العنوان
        if (label == 'العنوان') {
          if (v.length < 10 || v.length > 500) {
            return 'العنوان يجب أن يكون بين 10 و 500 حرف';
          }
        }

        return null;
      },
    );
  }

  Widget _buildGovernorateDropdown() {
    final textColor = _isDarkMode ? Colors.white : kDarkGrey;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: kInDriveGreen),
        color: _isDarkMode ? Colors.grey[700] : Colors.grey[50],
      ),
      child: DropdownButtonFormField<String>(
        value: _selectedGovernorate,
        decoration: const InputDecoration(
          labelText: 'المحافظة',
          prefixIcon: Icon(Icons.location_city, color: kInDriveGreen),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          labelStyle: TextStyle(
            fontFamily: 'Tajawal',
            color: kInDriveGreen,
          ),
        ),
        dropdownColor: _isDarkMode ? Colors.grey[800] : Colors.white,
        style: TextStyle(
          fontFamily: 'Tajawal',
          color: textColor,
        ),
        items: _governorateOptions.map((String governorate) {
          return DropdownMenuItem<String>(
            value: governorate,
            child: Row(
              children: [
                const Icon(Icons.location_on, color: Color(0xFF00BF63), size: 20),
                const SizedBox(width: 8),
                Text(governorate),
              ],
            ),
          );
        }).toList(),
        onChanged: (String? newValue) {
          setState(() {
            _selectedGovernorate = newValue!;
          });
        },
        validator: (v) => v == null ? 'يرجى اختيار المحافظة' : null,
      ),
    );
  }


}
