import 'package:flutter/material.dart';
import 'dart:math';
import 'dart:ui';
import 'home_screen.dart';

class OrderTrackingScreen extends StatefulWidget {
  final bool isDark;
  final String? pharmacyAddress;
  final String? userAddress;
  final VoidCallback onToggleDarkMode;

  const OrderTrackingScreen({
    Key? key,
    required this.isDark,
    this.pharmacyAddress,
    this.userAddress,
    required this.onToggleDarkMode,
  }) : super(key: key);

  @override
  State<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

class _OrderTrackingScreenState extends State<OrderTrackingScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _bikeAnimation;
  late Animation<double> _pulseAnimation;
  bool _showDetails = false;
  int _estimatedTime = 30; // دقيقة
  final double _deliveryProgress = 0.65; // 65% من الطريق

  // بيانات الطلبات الوهمية
  final List<Map<String, dynamic>> _orders = [
    {
      'id': '1',
      'pharmacy': 'صيدلية النور',
      'pharmacyAddress': 'شارع التحرير، القاهرة',
      'userAddress': 'مدينة نصر، القاهرة',
      'status': 'جار التوصيل',
      'time': 'منذ 5 دقائق',
      'medicines': ['باراسيتامول', 'إيبوبروفين', 'فيتامين سي'],
      'driver': {'name': 'أحمد محمد', 'phone': '+20123456789', 'rating': 4.8},
    },
    {
      'id': '2',
      'pharmacy': 'صيدلية الشفاء',
      'pharmacyAddress': 'شارع الثورة، الجيزة',
      'userAddress': 'المهندسين، الجيزة',
      'status': 'تم التسليم',
      'time': 'منذ ساعة',
      'medicines': ['أموكسيسيلين', 'دواء السعال'],
    },
    {
      'id': '3',
      'pharmacy': 'صيدلية الأمل',
      'pharmacyAddress': 'شارع النصر، القاهرة',
      'userAddress': 'مصر الجديدة، القاهرة',
      'status': 'ملغي',
      'time': 'منذ يوم',
      'medicines': ['فيتامين د'],
    },
  ];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..repeat(reverse: true);

    _bikeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _pulseAnimation = Tween<double>(
      begin: 0.9,
      end: 1.1,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = widget.isDark ? const Color(0xFF121212) : Colors.white;
    final textColor = widget.isDark ? Colors.white : const Color(0xFF121212);
    final cardColor = widget.isDark ? const Color(0xFF1E1E1E) : Colors.white;
    final borderColor = widget.isDark ? Colors.grey[800]! : Colors.grey[300]!;
    const kPrimaryColor = Color(0xFF00BF63);

    // إذا لم يتم تمرير عناوين، اعرض قائمة الطلبات
    if (widget.pharmacyAddress == null || widget.userAddress == null) {
      return Scaffold(
        backgroundColor: bgColor,
        appBar: _buildAppBar(bgColor, textColor, 'الطلبات'),
        body: ListView.separated(
          padding: const EdgeInsets.all(16),
          itemCount: _orders.length,
          separatorBuilder: (_, __) => const SizedBox(height: 12),
          itemBuilder: (context, i) {
            final order = _orders[i];
            final statusInfo = _getStatusInfo(order['status']);
            return GestureDetector(
              onTap: order['status'] == 'جار التوصيل'
                  ? () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => OrderTrackingScreen(
                          isDark: widget.isDark,
                          pharmacyAddress: order['pharmacyAddress'],
                          userAddress: order['userAddress'],
                          onToggleDarkMode: widget.onToggleDarkMode,
                        ),
                      ),
                    )
                  : null,
              child: Container(
                decoration: BoxDecoration(
                  color: cardColor,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: borderColor),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: kPrimaryColor.withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.local_pharmacy,
                            color: kPrimaryColor,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                order['pharmacy'],
                                style: TextStyle(
                                  color: textColor,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'Tajawal',
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                order['time'],
                                style: TextStyle(
                                  color: textColor.withValues(alpha: 0.6),
                                  fontFamily: 'Tajawal',
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: statusInfo.color.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                statusInfo.icon,
                                size: 16,
                                color: statusInfo.color,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                order['status'],
                                style: TextStyle(
                                  color: statusInfo.color,
                                  fontFamily: 'Tajawal',
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    if (order['medicines'] != null)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'الأدوية:',
                            style: TextStyle(
                              color: textColor.withValues(alpha: 0.8),
                              fontFamily: 'Tajawal',
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            order['medicines'].join('، '),
                            style: TextStyle(
                              color: textColor.withValues(alpha: 0.6),
                              fontFamily: 'Tajawal',
                              fontSize: 12,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      );
    }

    // إذا تم تمرير عناوين، اعرض أنيميشن التتبع
    return Scaffold(
      backgroundColor: bgColor,
      appBar: _buildAppBar(bgColor, textColor, 'تتبع الطلب'),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // معلومات التوصيل
                    Container(
                      decoration: BoxDecoration(
                        color: cardColor,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: borderColor),
                      ),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: kPrimaryColor.withValues(alpha: 0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.local_pharmacy,
                                  color: kPrimaryColor,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'صيدلية النور',
                                  style: TextStyle(
                                    color: textColor,
                                    fontWeight: FontWeight.bold,
                                    fontFamily: 'Tajawal',
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: kPrimaryColor.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'جار التوصيل',
                                  style: TextStyle(
                                    color: kPrimaryColor,
                                    fontFamily: 'Tajawal',
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          _buildDeliveryProgress(textColor),
                          const SizedBox(height: 16),
                          // تفاصيل السائق
                          if (_orders[0]['driver'] != null)
                            Column(
                              children: [
                                const Divider(),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 20,
                                      backgroundColor: kPrimaryColor
                                          .withValues(alpha: 0.1),
                                      child: Icon(
                                        Icons.person,
                                        color: kPrimaryColor,
                                        size: 20,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            _orders[0]['driver']['name'],
                                            style: TextStyle(
                                              color: textColor,
                                              fontFamily: 'Tajawal',
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.star,
                                                color: Colors.amber,
                                                size: 16,
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                _orders[0]['driver']['rating']
                                                    .toString(),
                                                style: TextStyle(
                                                  color: textColor.withValues(alpha: 
                                                    0.7,
                                                  ),
                                                  fontFamily: 'Tajawal',
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    IconButton(
                                      icon: Icon(
                                        Icons.phone,
                                        color: kPrimaryColor,
                                      ),
                                      onPressed: () {
                                        // TODO: Implement call functionality
                                      },
                                    ),
                                  ],
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    // خريطة التتبع
                    Container(
                      height: 300,
                      decoration: BoxDecoration(
                        color: cardColor,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: borderColor),
                      ),
                      child: Stack(
                        children: [
                          // هنا يجب استبدالها بخريطة حقيقية
                          Center(
                            child: Text(
                              'خريطة التتبع ستظهر هنا',
                              style: TextStyle(
                                color: textColor.withValues(alpha: 0.5),
                                fontFamily: 'Tajawal',
                              ),
                            ),
                          ),
                          // العناصر العلوية على الخريطة
                          Positioned(
                            top: 16,
                            left: 16,
                            child: _AddressBubble(
                              address: widget.pharmacyAddress!,
                              label: 'الصيدلية',
                              color: Colors.blueAccent,
                              textColor: Colors.white,
                            ),
                          ),
                          Positioned(
                            bottom: 16,
                            right: 16,
                            child: _AddressBubble(
                              address: widget.userAddress!,
                              label: 'عنوانك',
                              color: kPrimaryColor,
                              textColor: Colors.white,
                            ),
                          ),
                          // حركة الموتوسيكل
                          AnimatedBuilder(
                            animation: _bikeAnimation,
                            builder: (context, child) {
                              return Positioned(
                                left: lerpDouble(
                                  30,
                                  MediaQuery.of(context).size.width - 90,
                                  _deliveryProgress,
                                )!,
                                top: lerpDouble(
                                  30,
                                  MediaQuery.of(context).size.height * 0.2,
                                  _deliveryProgress,
                                )!,
                                child: Transform.scale(
                                  scale: _pulseAnimation.value,
                                  child: child,
                                ),
                              );
                            },
                            child: const _BikeWidget(),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    // الأدوية المطلوبة
                    Container(
                      decoration: BoxDecoration(
                        color: cardColor,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: borderColor),
                      ),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.medication_outlined,
                                color: kPrimaryColor,
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'الأدوية المطلوبة',
                                style: TextStyle(
                                  color: textColor,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'Tajawal',
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          ..._orders[0]['medicines'].map<Widget>(
                            (medicine) => Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.circle,
                                    size: 8,
                                    color: kPrimaryColor,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    medicine,
                                    style: TextStyle(
                                      color: textColor.withValues(alpha: 0.8),
                                      fontFamily: 'Tajawal',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // زر الإجراءات
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: cardColor,
              border: Border(top: BorderSide(color: borderColor)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      // TODO: Implement cancel order
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.withValues(alpha: 0.1),
                      foregroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(color: Colors.red.withValues(alpha: 0.3)),
                      ),
                    ),
                    child: const Text(
                      'إلغاء الطلب',
                      style: TextStyle(
                        fontFamily: 'Tajawal',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      // TODO: Implement contact driver
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kPrimaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'الاتصال بالسائق',
                      style: TextStyle(
                        fontFamily: 'Tajawal',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  AppBar _buildAppBar(Color bgColor, Color textColor, String title) {
    return AppBar(
      backgroundColor: bgColor,
      elevation: 0,
      iconTheme: const IconThemeData(color: Color(0xFF00BF63)),
      title: Text(
        title,
        style: TextStyle(
          color: textColor,
          fontFamily: 'Tajawal',
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        IconButton(
          icon: Icon(
            widget.isDark ? Icons.light_mode : Icons.dark_mode,
            color: const Color(0xFF00BF63),
          ),
          onPressed: widget.onToggleDarkMode,
        ),
      ],
    );
  }

  Widget _buildDeliveryProgress(Color textColor) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'وقت التوصيل المتوقع',
              style: TextStyle(
                color: textColor.withValues(alpha: 0.8),
                fontFamily: 'Tajawal',
              ),
            ),
            Text(
              '$_estimatedTime دقيقة',
              style: TextStyle(
                color: const Color(0xFF00BF63),
                fontFamily: 'Tajawal',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        LinearProgressIndicator(
          value: _deliveryProgress,
          backgroundColor: const Color(0xFF00BF63).withValues(alpha: 0.1),
          color: const Color(0xFF00BF63),
          minHeight: 8,
          borderRadius: BorderRadius.circular(4),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الصيدلية',
              style: TextStyle(
                color: textColor.withValues(alpha: 0.6),
                fontFamily: 'Tajawal',
                fontSize: 12,
              ),
            ),
            Text(
              '${(_deliveryProgress * 100).toInt()}%',
              style: TextStyle(
                color: textColor.withValues(alpha: 0.6),
                fontFamily: 'Tajawal',
                fontSize: 12,
              ),
            ),
            Text(
              'عنوانك',
              style: TextStyle(
                color: textColor.withValues(alpha: 0.6),
                fontFamily: 'Tajawal',
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }

  _StatusInfo _getStatusInfo(String status) {
    switch (status) {
      case 'تم التسليم':
        return _StatusInfo(Colors.green, Icons.check_circle);
      case 'ملغي':
        return _StatusInfo(Colors.red, Icons.cancel);
      default:
        return _StatusInfo(const Color(0xFF00BF63), Icons.delivery_dining);
    }
  }
}

class _StatusInfo {
  final Color color;
  final IconData icon;

  _StatusInfo(this.color, this.icon);
}

class _BikeWidget extends StatelessWidget {
  const _BikeWidget();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: const Icon(
        Icons.delivery_dining,
        size: 32,
        color: Color(0xFF00BF63),
      ),
    );
  }
}

class _AddressBubble extends StatelessWidget {
  final String address;
  final String label;
  final Color color;
  final Color textColor;

  const _AddressBubble({
    required this.address,
    required this.label,
    required this.color,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal',
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          SizedBox(
            width: 120,
            child: Text(
              address,
              style: TextStyle(
                color: textColor.withValues(alpha: 0.9),
                fontFamily: 'Tajawal',
                fontSize: 10,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
