import 'package:flutter/material.dart';
import 'package:pharmacy/widgets/animated_input_field.dart';
import 'package:pharmacy/widgets/animated_button.dart';
import 'package:pharmacy/screens/offers_screen.dart';
import 'package:pharmacy/screens/home_screen.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class RequestMedicineScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;

  const RequestMedicineScreen({
    super.key,
    this.isDark = true,
    required this.onToggleDarkMode,
  });

  @override
  _RequestMedicineScreenState createState() => _RequestMedicineScreenState();
}

class _RequestMedicineScreenState extends State<RequestMedicineScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _medicinesController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isLoading = false;
  File? _prescriptionImage;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.2), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutBack,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _medicinesController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = widget.isDark ? const Color(0xFF121212) : Colors.white;
    final textColor = widget.isDark ? Colors.white : const Color(0xFF121212);
    final cardColor = widget.isDark ? const Color(0xFF1E1E1E) : Colors.white;
    final borderColor = widget.isDark ? Colors.grey[800]! : Colors.grey[300]!;

    return Scaffold(
      backgroundColor: bgColor,
      appBar: _buildAppBar(bgColor, textColor),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              padding: const EdgeInsets.all(20),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    _buildMedicineInput(textColor, cardColor, borderColor),
                    const SizedBox(height: 20),
                    _buildNotesInput(textColor, cardColor, borderColor),
                    const SizedBox(height: 20),
                    _buildImageUpload(textColor, cardColor),
                    const SizedBox(height: 30),
                    _buildSubmitButton(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar(Color bgColor, Color textColor) {
    return AppBar(
      backgroundColor: bgColor,
      elevation: 0,
      iconTheme: IconThemeData(color: const Color(0xFF00BF63)),
      title: Text(
        'طلب دواء جديد',
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.bold,
          fontFamily: 'Tajawal',
          fontSize: 20,
        ),
      ),
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: textColor),
        onPressed: () => Navigator.of(context).pop(),
      ),
    );
  }

  Widget _buildMedicineInput(
    Color textColor,
    Color cardColor,
    Color borderColor,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: borderColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.medication, color: const Color(0xFF00BF63), size: 24),
              const SizedBox(width: 8),
              Text(
                'الأدوية المطلوبة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF00BF63),
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          AnimatedInputField(
            controller: _medicinesController,
            hintText: 'أدخل أسماء الأدوية (مفصولة بفواصل)',
            icon: Icons.medication_outlined,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يجب إدخال أسماء الأدوية';
              }
              return null;
            },
          ),
          const SizedBox(height: 8),
          Text(
            'مثال: باراسيتامول، إيبوبروفين، فيتامين سي',
            style: TextStyle(
              fontSize: 12,
              color: textColor.withValues(alpha: 0.6),
              fontFamily: 'Tajawal',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesInput(Color textColor, Color cardColor, Color borderColor) {
    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: borderColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.note_alt_outlined,
                color: const Color(0xFF00BF63),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'ملاحظات إضافية',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF00BF63),
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextFormField(
            controller: _notesController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'أي ملاحظات إضافية عن الطلب (اختياري)',
              hintStyle: TextStyle(
                color: textColor.withValues(alpha: 0.5),
                fontFamily: 'Tajawal',
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: const Color(0xFF00BF63).withValues(alpha: 0.3),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: const Color(0xFF00BF63).withValues(alpha: 0.3),
                ),
              ),
              contentPadding: const EdgeInsets.all(16),
            ),
            style: TextStyle(color: textColor, fontFamily: 'Tajawal'),
          ),
        ],
      ),
    );
  }

  Widget _buildImageUpload(Color textColor, Color cardColor) {
    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF00BF63).withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.photo_camera,
                color: const Color(0xFF00BF63),
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'صورة الروشتة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF00BF63),
                  fontFamily: 'Tajawal',
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '(اختياري)',
                style: TextStyle(
                  fontSize: 14,
                  color: textColor.withValues(alpha: 0.6),
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          GestureDetector(
            onTap: _showImagePickerOptions,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: 180,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: widget.isDark
                    ? const Color(0xFF242424)
                    : Colors.grey[100],
                border: Border.all(
                  color: const Color(0xFF00BF63).withValues(alpha: 0.3),
                  width: 1.5,
                  style: BorderStyle.solid,
                ),
              ),
              child: _prescriptionImage == null
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: const Color(0xFF00BF63).withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.add_photo_alternate_outlined,
                            size: 36,
                            color: Color(0xFF00BF63),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          'اضغط لإضافة صورة الروشتة',
                          style: TextStyle(
                            fontSize: 14,
                            color: textColor.withValues(alpha: 0.8),
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Tajawal',
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'JPG, PNG (حتى 5MB)',
                          style: TextStyle(
                            fontSize: 12,
                            color: textColor.withValues(alpha: 0.5),
                            fontFamily: 'Tajawal',
                          ),
                        ),
                      ],
                    )
                  : ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.file(
                        _prescriptionImage!,
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                      ),
                    ),
            ),
          ),
          if (_prescriptionImage != null)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => setState(() => _prescriptionImage = null),
                    style: TextButton.styleFrom(foregroundColor: Colors.red),
                    child: const Text(
                      'إزالة الصورة',
                      style: TextStyle(fontFamily: 'Tajawal', fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return AnimatedButton(
      onPressed: _isLoading ? null : _submitRequest,
      text: 'إرسال الطلب',
      isLoading: _isLoading,
    );
  }

  Future<void> _showImagePickerOptions() async {
    final result = await showModalBottomSheet<int>(
      context: context,
      backgroundColor: widget.isDark ? const Color(0xFF1E1E1E) : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.camera_alt, color: const Color(0xFF00BF63)),
              title: const Text(
                'التقاط صورة',
                style: TextStyle(fontFamily: 'Tajawal'),
              ),
              onTap: () => Navigator.pop(context, 0),
            ),
            ListTile(
              leading: Icon(
                Icons.photo_library,
                color: const Color(0xFF00BF63),
              ),
              title: const Text(
                'اختيار من المعرض',
                style: TextStyle(fontFamily: 'Tajawal'),
              ),
              onTap: () => Navigator.pop(context, 1),
            ),
            const Divider(height: 1),
            ListTile(
              leading: const Icon(Icons.close),
              title: const Text(
                'إلغاء',
                style: TextStyle(fontFamily: 'Tajawal'),
              ),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );

    if (result != null) {
      final picker = ImagePicker();
      final pickedFile = await (result == 0
          ? picker.pickImage(source: ImageSource.camera)
          : picker.pickImage(source: ImageSource.gallery));

      if (pickedFile != null) {
        setState(() {
          _prescriptionImage = File(pickedFile.path);
        });
      }
    }
  }

  Future<void> _submitRequest() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
            'تم إرسال طلبك بنجاح!',
            style: TextStyle(color: Colors.white, fontFamily: 'Tajawal'),
          ),
          backgroundColor: const Color(0xFF00BF63),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          duration: const Duration(seconds: 2),
        ),
      );

      await Future.delayed(const Duration(milliseconds: 1500));

      if (!mounted) return;

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => OffersScreen(
            requestId: DateTime.now().millisecondsSinceEpoch.toString(),
            isDark: widget.isDark,
            onToggleDarkMode: widget.onToggleDarkMode,
          ),
        ),
      );
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'حدث خطأ: ${e.toString()}',
            style: const TextStyle(color: Colors.white, fontFamily: 'Tajawal'),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
